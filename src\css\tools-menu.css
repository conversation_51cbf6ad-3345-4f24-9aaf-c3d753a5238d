/* 🎨 超现代化工具栏设计 - 毛玻璃效果 + 霓虹灯风格 */
.tools-toolbar {
    position: fixed;
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    background: linear-gradient(145deg,
        rgba(20, 20, 30, 0.95),
        rgba(30, 30, 45, 0.9)
    );
    backdrop-filter: blur(20px) saturate(180%);
    border-radius: 20px;
    padding: 8px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    z-index: 1000;
    border: 1px solid rgba(255, 255, 255, 0.15);
    animation: toolbarGlow 3s ease-in-out infinite alternate;
}

@keyframes toolbarGlow {
    0% { box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(64, 224, 255, 0.1); }
    100% { box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(64, 224, 255, 0.2); }
}

/* 🔥 超炫酷工具按钮 */
.tool-btn {
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.05),
        rgba(255, 255, 255, 0.02)
    );
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.85);
    padding: 16px 12px;
    margin: 4px 0;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 64px;
    height: 64px;
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.tool-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    transition: left 0.6s ease;
}

.tool-btn:hover::before {
    left: 100%;
}

.tool-btn:hover {
    background: linear-gradient(145deg,
        rgba(64, 224, 255, 0.15),
        rgba(100, 149, 237, 0.1)
    );
    border-color: rgba(64, 224, 255, 0.4);
    color: #ffffff;
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 12px 24px rgba(64, 224, 255, 0.2),
        0 6px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tool-btn.active {
    background: linear-gradient(145deg,
        rgba(64, 224, 255, 0.25),
        rgba(100, 149, 237, 0.2)
    );
    border-color: rgba(64, 224, 255, 0.6);
    color: #40E0FF;
    transform: scale(1.1);
    box-shadow:
        0 0 30px rgba(64, 224, 255, 0.4),
        0 8px 16px rgba(64, 224, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
    0% { box-shadow:
        0 0 20px rgba(64, 224, 255, 0.3),
        0 8px 16px rgba(64, 224, 255, 0.2); }
    100% { box-shadow:
        0 0 40px rgba(64, 224, 255, 0.5),
        0 8px 16px rgba(64, 224, 255, 0.3); }
}

/* ✨ 图标和文字样式 */
.tool-btn i {
    font-size: 22px;
    opacity: 0.9;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    z-index: 2;
    position: relative;
}

.tool-btn:hover i {
    opacity: 1;
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 4px 8px rgba(64, 224, 255, 0.3));
}

.tool-btn.active i {
    opacity: 1;
    transform: scale(1.15);
    filter: drop-shadow(0 4px 12px rgba(64, 224, 255, 0.5));
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1.15); }
    50% { transform: scale(1.2); }
}

.tool-btn span {
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    line-height: 1;
    opacity: 0.9;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    z-index: 2;
    position: relative;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.tool-btn:hover span,
.tool-btn.active span {
    opacity: 1;
    text-shadow: 0 0 8px rgba(64, 224, 255, 0.6);
}

/* 🌟 超炫酷工具面板 */
.tool-panel {
    position: fixed;
    top: 50%;
    right: 120px;
    transform: translateY(-50%);
    width: 420px;
    max-height: 85vh;
    background: linear-gradient(145deg,
        rgba(15, 15, 25, 0.98),
        rgba(25, 25, 40, 0.95)
    );
    border: 1px solid rgba(64, 224, 255, 0.3);
    border-radius: 24px;
    backdrop-filter: blur(25px) saturate(180%);
    box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.5),
        0 15px 40px rgba(0, 0, 0, 0.3),
        0 0 40px rgba(64, 224, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-50%) translateX(50px) scale(0.9) rotateY(10deg);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    z-index: 999;
    perspective: 1000px;
}

.tool-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(-50%) translateX(0) scale(1) rotateY(0deg);
}

.tool-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        transparent,
        rgba(64, 224, 255, 0.8),
        rgba(100, 149, 237, 0.6),
        transparent
    );
    animation: panelGlow 3s ease-in-out infinite;
}

@keyframes panelGlow {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* 💎 面板头部设计 */
.tool-panel-header {
    background: linear-gradient(135deg,
        rgba(64, 224, 255, 0.1),
        rgba(100, 149, 237, 0.05)
    );
    padding: 24px 28px;
    border-bottom: 1px solid rgba(64, 224, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(12px);
    position: relative;
}

.tool-panel-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(64, 224, 255, 0.6),
        transparent
    );
}

.tool-panel-title {
    color: #40E0FF;
    font-size: 20px;
    font-weight: 800;
    display: flex;
    align-items: center;
    gap: 16px;
    text-shadow:
        0 0 20px rgba(64, 224, 255, 0.5),
        0 2px 4px rgba(0, 0, 0, 0.3);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    letter-spacing: 0.5px;
}

.tool-panel-title i {
    font-size: 24px;
    filter: drop-shadow(0 0 10px rgba(64, 224, 255, 0.6));
    animation: titleIconFloat 3s ease-in-out infinite;
}

@keyframes titleIconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
}

.tool-panel-close {
    background: linear-gradient(145deg,
        rgba(255, 87, 87, 0.1),
        rgba(255, 87, 87, 0.05)
    );
    border: 1px solid rgba(255, 87, 87, 0.3);
    color: #fff;
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 16px;
    position: relative;
    overflow: hidden;
}

.tool-panel-close::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 87, 87, 0.2);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.tool-panel-close:hover::before {
    width: 100px;
    height: 100px;
}

.tool-panel-close:hover {
    background: linear-gradient(145deg,
        rgba(255, 87, 87, 0.25),
        rgba(255, 87, 87, 0.15)
    );
    border-color: #ff5757;
    color: #ff5757;
    transform: scale(1.1) rotate(-5deg);
    box-shadow: 0 8px 20px rgba(255, 87, 87, 0.3);
}

/* 🎯 面板内容区域 */
.tool-panel-content {
    padding: 28px;
    max-height: calc(85vh - 120px);
    overflow-y: auto;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.02),
        rgba(255, 255, 255, 0.01)
    );
    position: relative;
}

.tool-panel-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 28px;
    right: 28px;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(64, 224, 255, 0.3),
        transparent
    );
}

/* 🌈 超炫酷自定义滚动条 */
.tool-panel-content::-webkit-scrollbar {
    width: 8px;
}

.tool-panel-content::-webkit-scrollbar-track {
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.05),
        rgba(255, 255, 255, 0.02)
    );
    border-radius: 10px;
    border: 1px solid rgba(64, 224, 255, 0.1);
}

.tool-panel-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
        rgba(64, 224, 255, 0.8),
        rgba(100, 149, 237, 0.6)
    );
    border-radius: 10px;
    border: 1px solid rgba(64, 224, 255, 0.3);
    box-shadow:
        0 0 10px rgba(64, 224, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tool-panel-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
        rgba(64, 224, 255, 1),
        rgba(100, 149, 237, 0.8)
    );
    box-shadow:
        0 0 15px rgba(64, 224, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 面板特殊布局支持 */
.tool-panel .panel-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tool-panel .tab-btn {
    flex: 1;
    background: transparent;
    border: none;
    color: #ccc;
    padding: 12px 8px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
}

.tool-panel .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.tool-panel .tab-btn.active {
    background: rgba(0, 255, 255, 0.1);
    color: #00ffff;
    border-bottom-color: #00ffff;
}

.tool-panel .header-buttons {
    display: flex;
    gap: 4px;
    align-items: center;
}

.tool-panel .header-buttons .btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
}

.tool-panel .header-buttons .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #00ffff;
    color: #00ffff;
}

/* 🎨 悬浮粒子效果 */
.tools-toolbar::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle,
        rgba(64, 224, 255, 0.03) 1px,
        transparent 1px
    );
    background-size: 20px 20px;
    animation: particleFloat 20s linear infinite;
    pointer-events: none;
}

@keyframes particleFloat {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(-20px, -20px) rotate(360deg); }
}

/* 📱 响应式设计 */
@media (max-width: 768px) {
    .tools-toolbar {
        top: 20px;
        right: 15px;
        transform: none;
        border-radius: 16px;
        padding: 6px;
    }

    .tool-btn {
        width: 56px;
        height: 56px;
        padding: 12px 8px;
        gap: 4px;
        border-radius: 12px;
        margin: 2px 0;
    }

    .tool-btn i {
        font-size: 18px;
    }

    .tool-btn span {
        font-size: 9px;
    }

    .tool-panel {
        top: 20px;
        right: 80px;
        left: 15px;
        width: auto;
        max-height: 75vh;
        transform: translateY(0);
        border-radius: 20px;
    }

    .tool-panel.show {
        transform: translateY(0) translateX(0) scale(1) rotateY(0deg);
    }

    .tool-panel-title {
        font-size: 18px;
        gap: 12px;
    }

    .tool-panel-content {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .tools-toolbar {
        top: 50px;
        right: 10px;
    }
    
    .tool-btn {
        width: 50px;
        padding: 12px;
        gap: 4px;
    }
    
    .tool-btn i {
        font-size: 16px;
    }
    
    .tool-btn span {
        font-size: 9px;
    }
    
    .tool-panel {
        top: 50px;
        right: 10px;
        left: 10px;
        width: auto;
        max-height: 65vh;
    }
    
    .tool-panel-header {
        padding: 16px 20px;
    }
    
    .tool-panel-title {
        font-size: 15px;
        gap: 8px;
    }
    
    .tool-panel-content {
        padding: 16px;
    }
}

/* 🚀 额外的现代化效果 */
.tool-btn:active {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;
}

.tool-panel {
    animation: panelSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes panelSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-50%) translateX(100px) scale(0.8) rotateY(20deg);
    }
    100% {
        opacity: 1;
        transform: translateY(-50%) translateX(0) scale(1) rotateY(0deg);
    }
}

/* 🌟 工具栏入场动画 */
.tools-toolbar {
    animation: toolbarSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes toolbarSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-50%) translateX(100px) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translateY(-50%) translateX(0) scale(1);
    }
}

/* 💫 按钮点击波纹效果 */
.tool-btn {
    position: relative;
    overflow: hidden;
}

.tool-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(64, 224, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.tool-btn:active::after {
    width: 120px;
    height: 120px;
}

/* 🖥️ 大屏幕优化 */
@media (min-width: 1400px) {
    .tools-toolbar {
        right: 50px;
        padding: 10px;
    }

    .tool-btn {
        width: 72px;
        height: 72px;
        padding: 18px 14px;
        border-radius: 18px;
    }

    .tool-btn i {
        font-size: 24px;
    }

    .tool-btn span {
        font-size: 11px;
    }

    .tool-panel {
        right: 140px;
        width: 450px;
    }

    .tool-panel-header {
        padding: 28px 32px;
    }

    .tool-panel-content {
        padding: 32px;
    }
}