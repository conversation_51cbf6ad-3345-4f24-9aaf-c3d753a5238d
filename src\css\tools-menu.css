/* 🎨 超现代化工具栏设计 - 深色主题 + 霓虹灯风格 */
.tools-toolbar {
    position: fixed;
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    background: linear-gradient(145deg,
        rgba(15, 15, 25, 0.98),
        rgba(25, 25, 40, 0.95)
    );
    backdrop-filter: blur(25px) saturate(180%);
    border-radius: 20px;
    padding: 8px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.6),
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(64, 224, 255, 0.1),
        inset 0 1px 0 rgba(64, 224, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.3);
    z-index: 1000;
    border: 1px solid rgba(64, 224, 255, 0.3);
    animation: toolbarGlow 3s ease-in-out infinite alternate;
}

@keyframes toolbarGlow {
    0% { box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(64, 224, 255, 0.1); }
    100% { box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(64, 224, 255, 0.2); }
}

/* 🔥 超炫酷工具按钮 - 修复颜色问题 */
.tool-btn {
    background: linear-gradient(145deg,
        rgba(30, 30, 45, 0.9),
        rgba(20, 20, 35, 0.95)
    );
    border: 1px solid rgba(64, 224, 255, 0.3);
    color: #E0E0E0;
    padding: 16px 12px;
    margin: 4px 0;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 64px;
    height: 64px;
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.tool-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(64, 224, 255, 0.2),
        transparent
    );
    transition: left 0.6s ease;
    z-index: 1;
}

.tool-btn:hover::before {
    left: 100%;
}

.tool-btn:hover {
    background: linear-gradient(145deg,
        rgba(64, 224, 255, 0.2),
        rgba(30, 30, 50, 0.9)
    );
    border-color: rgba(64, 224, 255, 0.8);
    color: #FFFFFF;
    transform: translateY(-3px) scale(1.08);
    box-shadow:
        0 15px 35px rgba(64, 224, 255, 0.4),
        0 8px 16px rgba(0, 0, 0, 0.5),
        0 0 25px rgba(64, 224, 255, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    animation: buttonHover 0.3s ease-out;
}

@keyframes buttonHover {
    0% { transform: translateY(-2px) scale(1.05); }
    50% { transform: translateY(-4px) scale(1.1); }
    100% { transform: translateY(-3px) scale(1.08); }
}

.tool-btn.active {
    background: linear-gradient(145deg,
        rgba(64, 224, 255, 0.3),
        rgba(30, 30, 50, 0.95)
    );
    border-color: rgba(64, 224, 255, 1);
    color: #40E0FF;
    transform: scale(1.1);
    box-shadow:
        0 0 30px rgba(64, 224, 255, 0.6),
        0 8px 16px rgba(64, 224, 255, 0.3),
        0 0 15px rgba(64, 224, 255, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
    0% { box-shadow:
        0 0 20px rgba(64, 224, 255, 0.3),
        0 8px 16px rgba(64, 224, 255, 0.2); }
    100% { box-shadow:
        0 0 40px rgba(64, 224, 255, 0.5),
        0 8px 16px rgba(64, 224, 255, 0.3); }
}

/* ✨ 图标和文字样式 - 修复颜色 */
.tool-btn i {
    font-size: 22px;
    color: #B0B0B0;
    opacity: 1;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
    z-index: 2;
    position: relative;
}

.tool-btn:hover i {
    color: #40E0FF;
    opacity: 1;
    transform: scale(1.2) rotate(8deg);
    filter:
        drop-shadow(0 6px 12px rgba(64, 224, 255, 0.5))
        drop-shadow(0 0 15px rgba(64, 224, 255, 0.8));
    animation: iconFloat 0.6s ease-out;
}

@keyframes iconFloat {
    0% { transform: scale(1.1) rotate(5deg); }
    50% { transform: scale(1.25) rotate(10deg); }
    100% { transform: scale(1.2) rotate(8deg); }
}

.tool-btn.active i {
    color: #40E0FF;
    opacity: 1;
    transform: scale(1.25);
    filter:
        drop-shadow(0 6px 16px rgba(64, 224, 255, 0.7))
        drop-shadow(0 0 20px rgba(64, 224, 255, 1));
    animation: iconPulse 2s ease-in-out infinite, iconGlow 3s ease-in-out infinite alternate;
}

@keyframes iconGlow {
    0% {
        filter:
            drop-shadow(0 6px 16px rgba(64, 224, 255, 0.7))
            drop-shadow(0 0 20px rgba(64, 224, 255, 1));
    }
    100% {
        filter:
            drop-shadow(0 8px 20px rgba(64, 224, 255, 0.9))
            drop-shadow(0 0 30px rgba(64, 224, 255, 1.2));
    }
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1.15); }
    50% { transform: scale(1.2); }
}

.tool-btn span {
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    line-height: 1;
    color: #B0B0B0;
    opacity: 1;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    z-index: 2;
    position: relative;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
}

.tool-btn:hover span {
    color: #FFFFFF;
    opacity: 1;
    text-shadow: 0 0 8px rgba(64, 224, 255, 0.6);
}

.tool-btn.active span {
    color: #40E0FF;
    opacity: 1;
    text-shadow: 0 0 10px rgba(64, 224, 255, 0.8);
}

/* 🌟 超炫酷工具面板 - 侧边弹出 */
.tool-panel {
    position: fixed;
    top: calc(50% - 160px); /* 与工具栏顶端对齐 */
    right: 120px; /* 在工具栏左侧 */
    width: 420px;
    max-height: 85vh;
    background: linear-gradient(145deg,
        rgba(15, 15, 25, 0.98),
        rgba(25, 25, 40, 0.95)
    );
    border: 1px solid rgba(64, 224, 255, 0.3);
    border-radius: 24px;
    backdrop-filter: blur(25px) saturate(180%);
    box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.5),
        0 15px 40px rgba(0, 0, 0, 0.3),
        0 0 40px rgba(64, 224, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    opacity: 0;
    visibility: hidden;
    transform: translateX(30px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    z-index: 999;
}

.tool-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(0) scale(1);
}

.tool-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        transparent,
        rgba(64, 224, 255, 0.8),
        rgba(100, 149, 237, 0.6),
        transparent
    );
    animation: panelGlow 3s ease-in-out infinite;
}

@keyframes panelGlow {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* 💎 面板头部设计 */
.tool-panel-header {
    background: linear-gradient(135deg,
        rgba(64, 224, 255, 0.1),
        rgba(100, 149, 237, 0.05)
    );
    padding: 24px 28px;
    border-bottom: 1px solid rgba(64, 224, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(12px);
    position: relative;
}

.tool-panel-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(64, 224, 255, 0.6),
        transparent
    );
}

.tool-panel-title {
    color: #40E0FF;
    font-size: 20px;
    font-weight: 800;
    display: flex;
    align-items: center;
    gap: 16px;
    text-shadow:
        0 0 20px rgba(64, 224, 255, 0.5),
        0 2px 4px rgba(0, 0, 0, 0.3);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    letter-spacing: 0.5px;
}

.tool-panel-title i {
    font-size: 24px;
    filter: drop-shadow(0 0 10px rgba(64, 224, 255, 0.6));
    animation: titleIconFloat 3s ease-in-out infinite;
}

@keyframes titleIconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
}

.tool-panel-close {
    background: linear-gradient(145deg,
        rgba(255, 87, 87, 0.1),
        rgba(255, 87, 87, 0.05)
    );
    border: 1px solid rgba(255, 87, 87, 0.3);
    color: #fff;
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 16px;
    position: relative;
    overflow: hidden;
}

.tool-panel-close::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 87, 87, 0.2);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.tool-panel-close:hover::before {
    width: 100px;
    height: 100px;
}

.tool-panel-close:hover {
    background: linear-gradient(145deg,
        rgba(255, 87, 87, 0.25),
        rgba(255, 87, 87, 0.15)
    );
    border-color: #ff5757;
    color: #ff5757;
    transform: scale(1.1) rotate(-5deg);
    box-shadow: 0 8px 20px rgba(255, 87, 87, 0.3);
}

/* 🎯 面板内容区域 */
.tool-panel-content {
    padding: 28px;
    max-height: calc(85vh - 120px);
    overflow-y: auto;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.02),
        rgba(255, 255, 255, 0.01)
    );
    position: relative;
}

.tool-panel-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 28px;
    right: 28px;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(64, 224, 255, 0.3),
        transparent
    );
}

/* 🌈 超炫酷自定义滚动条 */
.tool-panel-content::-webkit-scrollbar {
    width: 8px;
}

.tool-panel-content::-webkit-scrollbar-track {
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.05),
        rgba(255, 255, 255, 0.02)
    );
    border-radius: 10px;
    border: 1px solid rgba(64, 224, 255, 0.1);
}

.tool-panel-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
        rgba(64, 224, 255, 0.8),
        rgba(100, 149, 237, 0.6)
    );
    border-radius: 10px;
    border: 1px solid rgba(64, 224, 255, 0.3);
    box-shadow:
        0 0 10px rgba(64, 224, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tool-panel-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
        rgba(64, 224, 255, 1),
        rgba(100, 149, 237, 0.8)
    );
    box-shadow:
        0 0 15px rgba(64, 224, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 面板特殊布局支持 */
.tool-panel .panel-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tool-panel .tab-btn {
    flex: 1;
    background: transparent;
    border: none;
    color: #ccc;
    padding: 12px 8px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
}

.tool-panel .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.tool-panel .tab-btn.active {
    background: rgba(0, 255, 255, 0.1);
    color: #00ffff;
    border-bottom-color: #00ffff;
}

.tool-panel .header-buttons {
    display: flex;
    gap: 4px;
    align-items: center;
}

.tool-panel .header-buttons .btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
}

.tool-panel .header-buttons .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #00ffff;
    color: #00ffff;
}

/* 🎨 悬浮粒子效果 */
.tools-toolbar::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle at 25% 25%, rgba(64, 224, 255, 0.05) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(100, 149, 237, 0.03) 1px, transparent 1px),
        radial-gradient(circle at 50% 50%, rgba(64, 224, 255, 0.02) 1.5px, transparent 1.5px);
    background-size: 30px 30px, 20px 20px, 25px 25px;
    animation: particleFloat 25s linear infinite;
    pointer-events: none;
    opacity: 0.6;
}

@keyframes particleFloat {
    0% { transform: translate(0, 0) rotate(0deg); opacity: 0.6; }
    50% { opacity: 0.8; }
    100% { transform: translate(-30px, -30px) rotate(360deg); opacity: 0.6; }
}

/* 🌟 工具栏边框发光效果 */
.tools-toolbar::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        rgba(64, 224, 255, 0.3),
        rgba(100, 149, 237, 0.2),
        rgba(64, 224, 255, 0.3)
    );
    border-radius: 22px;
    z-index: -1;
    animation: borderGlow 4s ease-in-out infinite alternate;
}

@keyframes borderGlow {
    0% { opacity: 0.3; transform: scale(1); }
    100% { opacity: 0.6; transform: scale(1.02); }
}

/* 📱 响应式设计 */
@media (max-width: 768px) {
    .tools-toolbar {
        top: 20px;
        right: 15px;
        transform: none;
        border-radius: 16px;
        padding: 6px;
    }

    .tool-btn {
        width: 56px;
        height: 56px;
        padding: 12px 8px;
        gap: 4px;
        border-radius: 12px;
        margin: 2px 0;
    }

    .tool-btn i {
        font-size: 18px;
    }

    .tool-btn span {
        font-size: 9px;
    }

    .tool-panel {
        top: 20px; /* 与工具栏顶端对齐 */
        right: 80px;
        left: 15px;
        width: auto;
        max-height: 75vh;
        transform: translateX(0);
        border-radius: 20px;
    }

    .tool-panel.show {
        transform: translateX(0) scale(1);
    }

    .tool-panel-title {
        font-size: 18px;
        gap: 12px;
    }

    .tool-panel-content {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .tools-toolbar {
        top: 50px;
        right: 10px;
    }
    
    .tool-btn {
        width: 50px;
        padding: 12px;
        gap: 4px;
    }
    
    .tool-btn i {
        font-size: 16px;
    }
    
    .tool-btn span {
        font-size: 9px;
    }
    
    .tool-panel {
        top: 50px; /* 与工具栏顶端对齐 */
        right: 10px;
        left: 10px;
        width: auto;
        max-height: 65vh;
    }
    
    .tool-panel-header {
        padding: 16px 20px;
    }
    
    .tool-panel-title {
        font-size: 15px;
        gap: 8px;
    }
    
    .tool-panel-content {
        padding: 16px;
    }
}

/* 🚀 额外的现代化效果 */
.tool-btn:active {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;
}

.tool-panel {
    animation: panelSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes panelSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-50%) translateX(100px) scale(0.8) rotateY(20deg);
    }
    100% {
        opacity: 1;
        transform: translateY(-50%) translateX(0) scale(1) rotateY(0deg);
    }
}

/* 🌟 工具栏入场动画 */
.tools-toolbar {
    animation: toolbarSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes toolbarSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-50%) translateX(100px) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translateY(-50%) translateX(0) scale(1);
    }
}

/* 💫 按钮点击波纹效果 */
.tool-btn {
    position: relative;
    overflow: hidden;
}

.tool-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(64, 224, 255, 0.4);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
    z-index: 1;
}

.tool-btn:active::after {
    width: 120px;
    height: 120px;
}

/* 🎯 工具提示样式 */
.tool-btn {
    position: relative;
}

.tool-btn:hover::before {
    content: attr(title);
    position: absolute;
    left: -120px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(145deg,
        rgba(10, 10, 20, 0.98),
        rgba(20, 20, 35, 0.95)
    );
    color: #E0E0E0;
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    z-index: 10001;
    border: 1px solid rgba(64, 224, 255, 0.5);
    backdrop-filter: blur(20px);
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(64, 224, 255, 0.3),
        inset 0 1px 0 rgba(64, 224, 255, 0.1);
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease-out 0.5s forwards;
    pointer-events: none;
}

@keyframes tooltipFadeIn {
    0% {
        opacity: 0;
        transform: translateY(-50%) translateX(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(-50%) translateX(0);
    }
}

/* 工具提示箭头 */
.tool-btn:hover::after {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid rgba(20, 20, 35, 0.95);
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    z-index: 10002;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease-out 0.5s forwards;
}

/* 🖥️ 大屏幕优化 */
@media (min-width: 1400px) {
    .tools-toolbar {
        right: 50px;
        padding: 10px;
    }

    .tool-btn {
        width: 72px;
        height: 72px;
        padding: 18px 14px;
        border-radius: 18px;
    }

    .tool-btn i {
        font-size: 24px;
    }

    .tool-btn span {
        font-size: 11px;
    }

    .tool-panel {
        right: 140px;
        width: 450px;
    }

    .tool-panel-header {
        padding: 28px 32px;
    }

    .tool-panel-content {
        padding: 32px;
    }
}