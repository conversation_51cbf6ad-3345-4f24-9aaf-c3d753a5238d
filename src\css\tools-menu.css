/* 工具栏样式 - 简洁垂直面板设计 */
.tools-toolbar {
    position: fixed;
    top: 80px;
    right: 20px;
    background: rgba(45, 45, 45, 0.9);
    backdrop-filter: blur(16px);
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.2),
        0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 工具按钮 */
.tool-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.9);
    padding: 16px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 70px;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    position: relative;
}

.tool-btn:last-child {
    border-bottom: none;
}

.tool-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.tool-btn.active {
    background: rgba(0, 255, 255, 0.15);
    color: #00ffff;
}

.tool-btn.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #00ffff;
    box-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
}

.tool-btn i {
    font-size: 20px;
    opacity: 0.9;
    transition: all 0.2s ease;
}

.tool-btn:hover i,
.tool-btn.active i {
    opacity: 1;
}

.tool-btn span {
    font-size: 11px;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
    opacity: 0.95;
}

/* 工具面板 */
.tool-panel {
    position: fixed;
    top: 80px;
    right: 100px; /* 调整为工具栏左侧 */
    width: 380px;
    max-height: 80vh;
    background: rgba(42, 42, 42, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    backdrop-filter: blur(16px);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 10px 20px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateX(30px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    z-index: 999;
}

.tool-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(0) scale(1);
}

.tool-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.5), transparent);
}

.tool-panel-header {
    background: rgba(255, 255, 255, 0.05);
    padding: 18px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(8px);
}

.tool-panel-title {
    color: #00ffff;
    font-size: 18px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.tool-panel-title i {
    font-size: 20px;
}

.tool-panel-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #fff;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 14px;
}

.tool-panel-close:hover {
    background: rgba(255, 87, 87, 0.2);
    border-color: #ff5757;
    color: #ff5757;
    transform: scale(1.05);
}

.tool-panel-content {
    padding: 24px;
    max-height: calc(80vh - 80px);
    overflow-y: auto;
}

/* 自定义滚动条 */
.tool-panel-content::-webkit-scrollbar {
    width: 6px;
}

.tool-panel-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.tool-panel-content::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 255, 0.5);
    border-radius: 3px;
}

.tool-panel-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 255, 0.7);
}

/* 面板特殊布局支持 */
.tool-panel .panel-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tool-panel .tab-btn {
    flex: 1;
    background: transparent;
    border: none;
    color: #ccc;
    padding: 12px 8px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
}

.tool-panel .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.tool-panel .tab-btn.active {
    background: rgba(0, 255, 255, 0.1);
    color: #00ffff;
    border-bottom-color: #00ffff;
}

.tool-panel .header-buttons {
    display: flex;
    gap: 4px;
    align-items: center;
}

.tool-panel .header-buttons .btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
}

.tool-panel .header-buttons .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #00ffff;
    color: #00ffff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tools-toolbar {
        top: 60px;
        right: 15px;
    }
    
    .tool-btn {
        width: 60px;
        padding: 14px;
        gap: 6px;
    }
    
    .tool-btn i {
        font-size: 18px;
    }
    
    .tool-btn span {
        font-size: 10px;
    }
    
    .tool-panel {
        top: 60px;
        right: 80px;
        left: 15px;
        width: auto;
        max-height: 70vh;
    }
    
    .tool-panel-title {
        font-size: 16px;
    }
    
    .tool-panel-content {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .tools-toolbar {
        top: 50px;
        right: 10px;
    }
    
    .tool-btn {
        width: 50px;
        padding: 12px;
        gap: 4px;
    }
    
    .tool-btn i {
        font-size: 16px;
    }
    
    .tool-btn span {
        font-size: 9px;
    }
    
    .tool-panel {
        top: 50px;
        right: 10px;
        left: 10px;
        width: auto;
        max-height: 65vh;
    }
    
    .tool-panel-header {
        padding: 16px 20px;
    }
    
    .tool-panel-title {
        font-size: 15px;
        gap: 8px;
    }
    
    .tool-panel-content {
        padding: 16px;
    }
}

@media (min-width: 1400px) {
    .tools-toolbar {
        right: 40px;
    }
    
    .tool-btn {
        width: 80px;
        padding: 18px;
    }
    
    .tool-btn i {
        font-size: 22px;
    }
    
    .tool-btn span {
        font-size: 12px;
    }
    
    .tool-panel {
        right: 130px;
        width: 420px;
    }
}