/* 🎮 超现代化工具按钮样式 */
#toolButtons {
    position: fixed;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 1000;
    padding: 8px;
    background: linear-gradient(145deg,
        rgba(20, 20, 30, 0.9),
        rgba(30, 30, 45, 0.8)
    );
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(64, 224, 255, 0.2);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(64, 224, 255, 0.1);
}

#toolButtons button {
    width: 48px;
    height: 48px;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    position: relative;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

#toolButtons button img {
    width: 24px;
    height: 24px;
    transition: all 0.3s ease;
}

#toolButtons button svg {
    width: 24px;
    height: 24px;
    transition: all 0.3s ease;
}

#toolButtons button:hover {
    background-color: #2196F3;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 保持现代化彩色图标的原始颜色 - 不应用滤镜 */
#toolButtons button:hover img {
    transform: scale(1.1);
    /* 移除滤镜，保持原始彩色 */
}

#toolButtons button:hover svg {
    transform: scale(1.1);
    /* SVG图标保持原始颜色 */
}

#toolButtons .tooltip {
    position: absolute;
    right: 60px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    white-space: nowrap;
    pointer-events: none;
}

#toolButtons button:hover .tooltip {
    opacity: 1;
    visibility: visible;
}

/* 特殊按钮状态 */
#toolButtons button.active {
    background-color: #2196F3;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

/* 活动状态也保持彩色图标 */
#toolButtons button.active img,
#toolButtons button.active svg {
    transform: scale(1.05);
    /* 移除滤镜，保持原始彩色 */
}

#toolButtons button.active:hover {
    background-color: #1976D2;
} 