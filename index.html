<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子沙盘展示</title>
    
    <!-- Cesium CDN -->
    <link href="node_modules/cesium/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <link rel="stylesheet" href="src/features/布局/坐标导航/navigation-combined.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/title.css">
    <link rel="stylesheet" href="src/css/buttons.css">
    <!-- Widget样式 -->
    <link rel="stylesheet" href="src/css/tools-menu.css">
    <link rel="stylesheet" href="src/widgets/measure/measure.css">
    <link rel="stylesheet" href="src/widgets/plot/plot.css">
    <link rel="stylesheet" href="src/widgets/bookmark/bookmark.css">
    <link rel="stylesheet" href="src/widgets/roam/roam.css">
    <link rel="stylesheet" href="src/widgets/datamanagement/datamanagement.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- JavaScript -->
    <script src="node_modules/cesium/Build/Cesium/Cesium.js"></script>
    <script src="https://unpkg.com/@turf/turf@6.5.0/turf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 可选：PDF生成库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script src="src/js/cesium.js"></script>
    <script src="src/features/布局/天空盒/SkyBoxManager.js"></script>
    <script src="src/features/布局/坐标导航/CesiumNavigation.umd.js"></script>
    <script src="src/features/布局/坐标导航/CoordinateDisplay.js"></script>
    <!-- Widget功能模块 -->
    <script src="src/widgets/measure/MeasureTool.js"></script>
    <script src="src/widgets/measure/MeasureUI.js"></script>
    <script src="src/widgets/plot/PlotTool.js"></script>
    <script src="src/widgets/plot/PlotUI.js"></script>
    <script src="src/widgets/bookmark/BookmarkTool.js"></script>
    <script src="src/widgets/bookmark/BookmarkUI.js"></script>
    <script src="src/widgets/roam/RoamTool.js"></script>
    <script src="src/widgets/roam/RoamUI.js"></script>
    <script src="src/widgets/share/ShareTool.js"></script>
    <script src="src/widgets/print/PrintTool.js"></script>
    <script src="src/widgets/datamanagement/DataManagementUI.js"></script>
</head>
<body>
    <!-- 标题图片容器 -->
    <div class="title-container">
        <img src="src/images/svg/title.svg" alt="标题">
        <div class="title-text">电子沙盘</div>
    </div>
    
    <!-- 🌟 超炫酷加载动画 -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="loading-circle"></div>
                <div class="loading-text">电子沙盘</div>
            </div>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="loading-status">正在初始化系统...</div>
            </div>
        </div>
        <div class="loading-particles"></div>
    </div>

    <div id="cesiumContainer"></div>
    
    <!-- 工具按钮组 - 按钮将由各UI组件动态添加 -->
    <div id="toolButtons">
        <!-- 工具栏 -->
        <div class="tools-toolbar" id="toolsToolbar">
            <button class="tool-btn" data-tool="measure" onclick="activateTool('measure')" title="测量工具 - 距离、面积、高度测量">
                <i class="fa fa-ruler-combined"></i>
                <span>测量</span>
            </button>
            <button class="tool-btn" data-tool="plot" onclick="activateTool('plot')" title="标绘工具 - 绘制点线面和标注">
                <i class="fa fa-draw-polygon"></i>
                <span>标绘</span>
            </button>
            <button class="tool-btn" data-tool="bookmark" onclick="activateTool('bookmark')" title="书签管理 - 保存和管理视点">
                <i class="fa fa-map-marker-alt"></i>
                <span>书签</span>
            </button>
            <button class="tool-btn" data-tool="roam" onclick="activateTool('roam')" title="漫游动画 - 路径飞行和动画">
                <i class="fa fa-route"></i>
                <span>漫游</span>
            </button>
            <button class="tool-btn" data-tool="data" onclick="activateTool('data')" title="数据管理 - 分享、打印、导出">
                <i class="fa fa-cogs"></i>
                <span>数据</span>
            </button>
        </div>
    </div>
    
    <!-- 🔔 现代化通知系统 -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- 引入SVG图标 -->
    <div id="svg-container"></div>
    
    <script>
        // 🔔 现代化通知系统
        function showNotification(title, message, type = 'info', duration = 4000) {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-title">${title}</div>
                    <div>${message}</div>
                </div>
            `;

            container.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                notification.classList.add('hide');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, duration);
        }

        // 🚀 超炫酷加载管理
        let loadingProgress = 0;
        const loadingSteps = [
            '正在加载Cesium引擎...',
            '正在初始化地球...',
            '正在加载地形数据...',
            '正在初始化工具模块...',
            '正在加载界面组件...',
            '系统初始化完成！'
        ];

        function updateLoadingProgress(step, progress) {
            const progressFill = document.querySelector('.progress-fill');
            const statusText = document.querySelector('.loading-status');

            if (progressFill) {
                progressFill.style.width = progress + '%';
            }
            if (statusText && loadingSteps[step]) {
                statusText.textContent = loadingSteps[step];
            }
        }

        function hideLoadingScreen() {
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                setTimeout(() => {
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 800);
                }, 500);
            }
        }

        // 等待页面加载完成
        window.onload = function() {
            try {
                // 模拟加载进度
                updateLoadingProgress(0, 20);

                setTimeout(() => {
                    updateLoadingProgress(1, 40);
                    // 初始化Cesium
                    const viewer = initCesium();
                
                    updateLoadingProgress(2, 60);

                    setTimeout(() => {
                        // 初始化坐标显示功能
                        window.coordinateDisplay = new CoordinateDisplay(viewer);
                        updateLoadingProgress(3, 70);

                        setTimeout(() => {
                            // 初始化Widget功能模块
                            window.measureUI = new MeasureUI(viewer, 'toolButtons');
                            console.log('✅ 测量工具初始化完成');
                            showNotification('系统初始化', '测量工具已就绪', 'success', 2000);

                            window.plotUI = new PlotUI(viewer, 'toolButtons');
                            console.log('✅ 标绘工具初始化完成');
                            showNotification('系统初始化', '标绘工具已就绪', 'success', 2000);

                            window.bookmarkUI = new BookmarkUI(viewer, 'toolButtons');
                            console.log('✅ 书签管理初始化完成');
                            showNotification('系统初始化', '书签管理已就绪', 'success', 2000);
                            updateLoadingProgress(4, 85);

                            setTimeout(() => {
                                // 初始化漫游工具
                                window.roamTool = new RoamTool(viewer);
                                window.roamUI = new RoamUI(viewer, window.roamTool);
                                console.log('✅ 漫游工具初始化完成');

                                // 初始化数据管理工具
                                window.shareTool = new ShareTool(viewer);
                                window.printTool = new PrintTool(viewer);
                                window.dataManagementUI = new DataManagementUI(viewer, window.shareTool, window.printTool, window.bookmarkUI.bookmarkTool);
                                console.log('✅ 数据管理工具初始化完成');

                                // 尝试从URL恢复共享状态
                                if (window.shareTool.restoreFromUrl()) {
                                    console.log('✅ 从URL恢复状态成功');
                                }
                                updateLoadingProgress(5, 100);

                                setTimeout(() => {
                                    hideLoadingScreen();
                                    showNotification('🎉 欢迎使用', '电子沙盘系统已成功启动！', 'success', 5000);
                                }, 800);
                            }, 500);
                        }, 500);
                    }, 500);
                }, 500);
                
                // 加载SVG图标
                fetch('src/images/svg/icons.svg')
                    .then(response => response.text())
                    .then(svgContent => {
                        document.getElementById('svg-container').innerHTML = svgContent;
                    })
                    .catch(error => console.error('加载SVG图标失败:', error));
                
                // 初始化工具栏
                initToolsToolbar();
                
            } catch (error) {
                console.error('初始化失败:', error);
            }
        };
        
        // 工具栏控制函数
        let currentActiveTool = null;
        
        function initToolsToolbar() {
            // 无需特殊初始化逻辑
        }


        
        // 🎵 音效反馈系统（可选）
        function playClickSound() {
            try {
                // 创建简单的点击音效
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            } catch (e) {
                // 静默处理音效错误
            }
        }

        function activateTool(toolName) {
            // 播放点击音效
            playClickSound();

            // 显示操作通知
            const toolNames = {
                'measure': '测量工具',
                'plot': '标绘工具',
                'bookmark': '书签管理',
                'roam': '漫游动画',
                'data': '数据管理'
            };

            // 关闭之前的工具
            if (currentActiveTool && currentActiveTool !== toolName) {
                deactivateCurrentTool();
                showNotification('工具切换', `已切换到${toolNames[toolName]}`, 'info', 2000);
            } else {
                showNotification('工具激活', `${toolNames[toolName]}已激活`, 'success', 2000);
            }

            // 更新按钮状态
            document.querySelectorAll('.tool-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            const activeBtn = document.querySelector(`[data-tool="${toolName}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }

            // 激活对应的工具
            currentActiveTool = toolName;

            switch (toolName) {
                case 'measure':
                    window.measureUI.toggle();
                    break;
                case 'plot':
                    window.plotUI.toggle();
                    break;
                case 'bookmark':
                    window.bookmarkUI.toggle();
                    break;
                case 'roam':
                    window.roamUI.togglePanel();
                    break;
                case 'data':
                    window.dataManagementUI.togglePanel();
                    break;
            }
        }
        
        function deactivateCurrentTool() {
            if (!currentActiveTool) return;
            
            switch (currentActiveTool) {
                case 'measure':
                    if (window.measureUI.panel && window.measureUI.panel.classList.contains('show')) {
                        window.measureUI.toggle();
                    }
                    break;
                case 'plot':
                    if (window.plotUI.panel && window.plotUI.panel.classList.contains('show')) {
                        window.plotUI.toggle();
                    }
                    break;
                case 'bookmark':
                    if (window.bookmarkUI.panel && window.bookmarkUI.panel.classList.contains('show')) {
                        window.bookmarkUI.toggle();
                    }
                    break;
                case 'roam':
                    if (window.roamUI.panel && window.roamUI.panel.classList.contains('show')) {
                        window.roamUI.togglePanel();
                    }
                    break;
                case 'data':
                    if (window.dataManagementUI.panel && window.dataManagementUI.panel.classList.contains('show')) {
                        window.dataManagementUI.togglePanel();
                    }
                    break;
            }
            
            // 清除按钮状态
            document.querySelectorAll('.tool-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            currentActiveTool = null;
        }
    </script>
</body>
</html>
