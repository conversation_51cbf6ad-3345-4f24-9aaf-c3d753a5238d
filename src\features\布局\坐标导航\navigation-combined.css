/* 导航和坐标显示组件样式 - 合并版本 */

/* ------------------- 坐标显示样式 - 简洁底部状态栏 ------------------- */
.coordinate-display {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    color: white;
    font-size: 11px;
    z-index: 1001;
    border: none;
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
    text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.8);
    padding: 3px 0;
    height: 24px;
    box-sizing: border-box;
}

.coordinate-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 16px;
    height: 100%;
    gap: 20px;
}

.coordinate-text {
    display: flex;
    gap: 20px;
    flex: 1;
}

.coordinate-text span {
    white-space: nowrap;
    display: flex;
    align-items: center;
    font-weight: 500;
    padding: 0 6px;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.95);
}

.coordinate-text span:hover {
    color: #4a90e2;
}

/* 简化标签样式 - 不使用SVG图标 */
.coordinate-text span::before {
    content: attr(data-label);
    margin-right: 4px;
    font-size: 9px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
    text-transform: uppercase;
}

/* 比例尺居中显示 */
.scale-display {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 0 8px;
}

.scale-display::before {
    content: "SCALE";
    font-size: 9px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
    margin-right: 2px;
}

.coordinate-system {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    font-size: 10px;
    font-weight: 500;
    height: 18px;
}

.coordinate-system option {
    background: #1a1a1a;
    color: white;
    padding: 2px;
}

.coordinate-system:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.coordinate-system:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.3);
}

.scale-icon-container {
    display: none; /* 不显示图标容器 */
}

.scale-bar {
    height: 2px;
    background: white;
    width: 50px;
    position: relative;
    border-radius: 1px;
}

.scale-bar:before, .scale-bar:after {
    content: '';
    position: absolute;
    height: 4px;
    width: 1px;
    background: white;
    top: -1px;
}

.scale-bar:before {
    left: 0;
}

.scale-bar:after {
    right: 0;
}

.scale-text {
    font-size: 9px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.8);
}

/* ------------------- 导航控件样式 ------------------- */
/* 隐藏原始距离图例 */
.distance-legend {
    display: none !important;
    pointer-events: auto;
    position: absolute;
    border-radius: 15px;
    padding-left: 5px;
    padding-right: 5px;
    left: 50%;
    transform: translateX(-50%);
    bottom: 90px;
    height: 30px;
    width: 125px;
    box-sizing: content-box;
    background: rgba(0, 0, 0, 0.6);
}

.distance-legend-label {
    display: inline-block;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: lighter;
    line-height: 30px;
    color: #FFFFFF;
    width: 125px;
    text-align: center;
}

.distance-legend-scale-bar {
    border-left: 1px solid #FFFFFF;
    border-right: 1px solid #FFFFFF;
    border-bottom: 1px solid #FFFFFF;
    position: absolute;
    height: 10px;
    top: 15px;
}

.navigation-controls {
    position: absolute;
    right: 30px;
    top: 210px;
    width: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 300;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.navigation-control {
    cursor: pointer;
    border-bottom: 1px solid #555555;
}

.naviagation-control:active {
    color: #FFF;
}

.navigation-control-last {
    cursor: pointer;
    border-bottom: 0;
}

.navigation-control-icon-zoom-in {
    position: relative;
    text-align: center;
    font-size: 20px;
    color: #FFFFFF;
    padding-bottom: 4px;
}

.navigation-control-icon-zoom-out {
    position: relative;
    text-align: center;
    font-size: 20px;
    color: #FFFFFF;
}

.navigation-control-icon-reset {
    position: relative;
    left: 10px;
    width: 10px;
    height: 10px;
    fill: rgba(255, 255, 255, 0.8);
    padding-top: 6px;
    padding-bottom: 6px;
    box-sizing: content-box;
}

.compass {
    pointer-events: auto;
    position: absolute;
    right: 0px;
    top: 100px;
    width: 95px;
    height: 95px;
    overflow: hidden;
}

.compass-outer-ring {
    position: absolute;
    top: 0;
    width: 95px;
    height: 95px;
    fill: rgba(255, 255, 255, 0.5);
}

.compass-outer-ring-background {
    position: absolute;
    top: 14px;
    left: 14px;
    width: 44px;
    height: 44px;
    border-radius: 44px;
    border: 12px solid rgba(47, 53, 60, 0.8);
    box-sizing: content-box;
}

.compass-gyro {
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 95px;
    height: 95px;
    fill: #CCC;
}

.compass-gyro-active {
    fill: #68ADFE;
}

.compass-gyro-background {
    position: absolute;
    top: 30px;
    left: 30px;
    width: 33px;
    height: 33px;
    border-radius: 33px;
    background-color: rgba(47, 53, 60, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-sizing: content-box;
}

.compass-gyro-background:hover + .compass-gyro {
    fill: #68ADFE;
}

.compass-rotation-marker {
    position: absolute;
    top: 0;
    width: 95px;
    height: 95px;
    fill: #68ADFE;
}

/* ------------------- 响应式媒体查询 ------------------- */
@media print {
    .coordinate-display,
    .distance-legend,
    .navigation-controls,
    .compass {
        display: none;
    }
}

/* 平板和小屏幕优化 */
@media screen and (max-width: 768px) {
    .coordinate-display {
        height: 28px;
        padding: 4px 0;
    }
    
    .coordinate-content {
        padding: 0 12px;
        gap: 16px;
    }
    
    .coordinate-text {
        gap: 16px;
    }
    
    .coordinate-text span {
        font-size: 10px;
        padding: 0 4px;
    }
    
    .coordinate-text span::before {
        font-size: 8px;
    }
    
    .scale-display {
        padding: 0 6px;
    }
    
    .coordinate-system {
        font-size: 9px;
        height: 16px;
    }
}

/* 超小屏幕优化 */
@media screen and (max-width: 480px) {
    .coordinate-display {
        height: 32px;
        padding: 6px 0;
    }
    
    .coordinate-content {
        flex-direction: column;
        gap: 4px;
    }
    
    .coordinate-text {
        gap: 12px;
        justify-content: center;
    }
    
    .coordinate-text span {
        font-size: 9px;
        padding: 0 3px;
    }
    
    .scale-display {
        order: 1;
    }
    
    .coordinate-system {
        font-size: 9px;
        height: 16px;
        order: 2;
    }
    
    .scale-bar {
        width: 40px;
    }
}

/* 超宽屏优化 */
@media screen and (min-width: 1400px) {
    .coordinate-content {
        max-width: 1600px;
        padding: 0 40px;
    }
    
    .coordinate-text {
        gap: 28px;
    }
    
    .coordinate-text span {
        font-size: 12px;
    }
} 