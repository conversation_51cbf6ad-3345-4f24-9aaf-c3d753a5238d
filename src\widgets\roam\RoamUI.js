/**
 * 漫游工具用户界面 - 基于原生DOM实现
 * 提供路径管理、飞行控制和图表显示界面
 */
class RoamUI {
    constructor(viewer, roamTool) {
        this.viewer = viewer;
        this.roamTool = roamTool;
        this.panel = null;
        this.currentMode = 'list'; // list, create, fly, chart
        this.creatingRoute = null;
        this.drawingHandler = null;
        this.chartInstance = null;
        
        this.setupEventListeners();
    }

    // 创建主面板
    createPanel() {
        if (this.panel) {
            this.panel.remove();
        }

        this.panel = document.createElement('div');
        this.panel.className = 'tool-panel';
        this.panel.innerHTML = `
            <div class="tool-panel-header">
                <div class="tool-panel-title">
                    <i class="fa fa-paper-plane"></i>
                    漫游管理
                </div>
                <div class="header-buttons">
                    <button class="btn btn-sm" onclick="roamUI.switchMode('list')" title="路径列表">
                        <i class="fa fa-list"></i>
                    </button>
                    <button class="btn btn-sm" onclick="roamUI.switchMode('create')" title="创建路径">
                        <i class="fa fa-plus"></i>
                    </button>
                    <button class="tool-panel-close" onclick="roamUI.closePanel()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="tool-panel-content">
                ${this.getContentHTML()}
            </div>
        `;

        document.body.appendChild(this.panel);

        // 添加show类以显示面板
        setTimeout(() => {
            this.panel.classList.add('show');
        }, 10);

        this.setupPanelEvents();
        return this.panel;
    }

    // 获取内容HTML
    getContentHTML() {
        switch (this.currentMode) {
            case 'list':
                return this.getRouteListHTML();
            case 'create':
                return this.getCreateRouteHTML();
            case 'fly':
                return this.getFlyControlHTML();
            case 'chart':
                return this.getChartHTML();
            default:
                return this.getRouteListHTML();
        }
    }

    // 路径列表界面
    getRouteListHTML() {
        const routes = this.roamTool.getAllRoutes();
        
        let routesHTML = '';
        if (routes.length === 0) {
            routesHTML = '<div class="no-data">暂无漫游路径，点击上方 + 按钮创建</div>';
        } else {
            routes.forEach(route => {
                routesHTML += `
                    <div class="route-item" data-route-id="${route.id}">
                        <div class="route-info">
                            <div class="route-name">${route.name}</div>
                            <div class="route-detail">
                                ${route.points.length} 个点 | 
                                ${new Date(route.createTime).toLocaleString()}
                            </div>
                            ${route.description ? `<div class="route-desc">${route.description}</div>` : ''}
                        </div>
                        <div class="route-actions">
                            <button class="btn btn-sm" onclick="roamUI.flyRoute('${route.id}')" title="开始飞行">
                                <i class="fa fa-play"></i>
                            </button>
                            <button class="btn btn-sm" onclick="roamUI.showChart('${route.id}')" title="高度剖面">
                                <i class="fa fa-bar-chart"></i>
                            </button>
                            <button class="btn btn-sm" onclick="roamUI.editRoute('${route.id}')" title="编辑">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="btn btn-sm" onclick="roamUI.exportRoute('${route.id}')" title="导出">
                                <i class="fa fa-download"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="roamUI.deleteRoute('${route.id}')" title="删除">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
        }

        return `
            <div class="route-list-container">
                <div class="list-actions">
                    <button class="btn btn-primary" onclick="roamUI.switchMode('create')">
                        <i class="fa fa-plus"></i> 新建路径
                    </button>
                    <button class="btn btn-secondary" onclick="roamUI.importRoute()">
                        <i class="fa fa-upload"></i> 导入路径
                    </button>
                </div>
                <div class="route-list">
                    ${routesHTML}
                </div>
            </div>
        `;
    }

    // 创建路径界面
    getCreateRouteHTML() {
        return `
            <div class="create-route-container">
                <form class="route-form" onsubmit="return roamUI.createRoute(event)">
                    <div class="form-group">
                        <label>路径名称:</label>
                        <input type="text" name="name" placeholder="请输入路径名称" required>
                    </div>
                    <div class="form-group">
                        <label>描述:</label>
                        <textarea name="description" placeholder="路径描述（可选）" rows="2"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>飞行时间(秒):</label>
                            <input type="number" name="duration" value="60" min="10" max="600">
                        </div>
                        <div class="form-group">
                            <label>飞行高度(米):</label>
                            <input type="number" name="height" value="1000" min="0" max="10000">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="clampToGround"> 贴地飞行
                        </label>
                    </div>
                    <div class="form-group">
                        <label>路径颜色:</label>
                        <input type="color" name="pathColor" value="#00ffff">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">创建并绘制路径</button>
                        <button type="button" class="btn btn-secondary" onclick="roamUI.switchMode('list')">取消</button>
                    </div>
                </form>
                <div class="drawing-tips">
                    <h4>绘制说明:</h4>
                    <p>• 创建路径后，在地图上点击绘制飞行路径</p>
                    <p>• 左键点击添加路径点</p>
                    <p>• 右键结束绘制</p>
                    <p>• 至少需要2个路径点</p>
                </div>
            </div>
        `;
    }

    // 飞行控制界面
    getFlyControlHTML() {
        const route = this.roamTool.currentRoute;
        if (!route) return '<div class="error">未找到当前飞行路径</div>';

        // 立即计算总距离
        const totalDistance = this.roamTool.calculateRouteDistance(route);
        const distanceText = totalDistance > 1000 ? 
            `${(totalDistance / 1000).toFixed(2)}km` : 
            `${Math.round(totalDistance)}m`;

        return `
            <div class="fly-control-container">
                <div class="route-info-header">
                    <h4>${route.name}</h4>
                    <div class="route-stats">
                        <span>总距离: <span id="total-distance">${distanceText}</span></span>
                        <span>总时间: ${route.duration}秒</span>
                    </div>
                </div>
                
                <div class="flight-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-info">
                        <span>进度: <span id="progress-percent">0%</span></span>
                        <span>剩余: <span id="remaining-time">00:00</span></span>
                    </div>
                </div>

                <div class="flight-info">
                    <div class="info-item">
                        <label>当前高度:</label>
                        <span id="current-height">0 m</span>
                    </div>
                    <div class="info-item">
                        <label>已飞行距离:</label>
                        <span id="flown-distance">0 m</span>
                    </div>
                    <div class="info-item">
                        <label>飞行速度:</label>
                        <span id="flight-speed">1x</span>
                    </div>
                </div>

                <div class="flight-controls">
                    <button class="btn btn-primary" id="play-pause-btn" onclick="roamUI.toggleFly()">
                        <i class="fa fa-pause"></i> 暂停
                    </button>
                    <button class="btn btn-secondary" onclick="roamUI.stopFly()">
                        <i class="fa fa-stop"></i> 停止
                    </button>
                    <button class="btn btn-secondary" onclick="roamUI.showChart('${route.id}')">
                        <i class="fa fa-bar-chart"></i> 剖面图
                    </button>
                </div>

                <div class="speed-control">
                    <label>播放速度:</label>
                    <div class="speed-buttons">
                        <button class="btn btn-sm" onclick="roamUI.setSpeed(0.5)">0.5x</button>
                        <button class="btn btn-sm active" onclick="roamUI.setSpeed(1)">1x</button>
                        <button class="btn btn-sm" onclick="roamUI.setSpeed(2)">2x</button>
                        <button class="btn btn-sm" onclick="roamUI.setSpeed(5)">5x</button>
                    </div>
                </div>
            </div>
        `;
    }

    // 图表界面
    getChartHTML() {
        return `
            <div class="chart-container">
                <div class="chart-header">
                    <h4>高度剖面图</h4>
                    <button class="btn btn-sm" onclick="roamUI.switchMode('list')">
                        <i class="fa fa-arrow-left"></i> 返回列表
                    </button>
                </div>
                <div class="chart-content">
                    <div id="roam-chart" style="width: 100%; height: 300px;"></div>
                    <div class="chart-info">
                        <div class="chart-stats" id="chart-stats"></div>
                    </div>
                </div>
            </div>
        `;
    }

    // 切换模式
    switchMode(mode) {
        this.currentMode = mode;
        this.updatePanel();
    }

    // 更新面板内容
    updatePanel() {
        if (!this.panel) return;
        
        const content = this.panel.querySelector('.tool-panel-content');
        if (content) {
            content.innerHTML = this.getContentHTML();
            this.setupContentEvents();
        }
    }

    // 创建路径
    createRoute(event) {
        event.preventDefault();
        const form = event.target;
        const formData = new FormData(form);
        
        const routeData = {
            name: formData.get('name'),
            description: formData.get('description') || '',
            duration: parseInt(formData.get('duration')),
            height: parseInt(formData.get('height')),
            clampToGround: formData.has('clampToGround'),
            pathColor: formData.get('pathColor')
        };

        this.creatingRoute = this.roamTool.createRoute(routeData.name, routeData.description);
        Object.assign(this.creatingRoute, routeData);
        
        this.startDrawingPath();
        return false;
    }

    // 开始绘制路径
    startDrawingPath() {
        this.viewer.cesiumWidget.canvas.style.cursor = 'crosshair';
        
        this.drawingHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        
        // 左键点击添加点
        this.drawingHandler.setInputAction((click) => {
            const position = this.viewer.camera.pickEllipsoid(click.position, this.viewer.scene.globe.ellipsoid);
            if (position) {
                this.roamTool.addRoutePoint(this.creatingRoute.id, position);
                this.updateDrawingVisual();
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

        // 右键结束绘制
        this.drawingHandler.setInputAction(() => {
            this.finishDrawing();
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
        
        this.showDrawingTips();
    }

    // 更新绘制可视化
    updateDrawingVisual() {
        if (!this.creatingRoute || this.creatingRoute.points.length === 0) return;

        const positions = this.creatingRoute.points.map(point => 
            new Cesium.Cartesian3(point.position.x, point.position.y, point.position.z)
        );

        // 移除旧的绘制实体
        const existingEntity = this.viewer.entities.getById('drawing_path');
        if (existingEntity) {
            this.viewer.entities.remove(existingEntity);
        }

        // 添加新的路径线
        this.viewer.entities.add({
            id: 'drawing_path',
            polyline: {
                positions: positions,
                width: 3,
                material: Cesium.Color.fromCssColorString(this.creatingRoute.pathColor || '#00ffff'),
                clampToGround: this.creatingRoute.clampToGround
            }
        });
    }

    // 完成绘制
    finishDrawing() {
        if (this.drawingHandler) {
            this.drawingHandler.destroy();
            this.drawingHandler = null;
        }
        
        this.viewer.cesiumWidget.canvas.style.cursor = '';
        this.hideDrawingTips();

        // 清理绘制实体
        const existingEntity = this.viewer.entities.getById('drawing_path');
        if (existingEntity) {
            this.viewer.entities.remove(existingEntity);
        }

        if (this.creatingRoute && this.creatingRoute.points.length >= 2) {
            this.roamTool.saveRoutes();
            this.switchMode('list');
            this.showMessage('路径创建成功！', 'success');
        } else {
            this.showMessage('至少需要2个路径点！', 'error');
        }
        
        this.creatingRoute = null;
    }

    // 显示绘制提示
    showDrawingTips() {
        const tips = document.createElement('div');
        tips.id = 'drawing-tips';
        tips.className = 'drawing-tips-overlay';
        tips.innerHTML = `
            <div class="tips-content">
                <h4>绘制路径中...</h4>
                <p>• 左键点击添加路径点</p>
                <p>• 右键结束绘制</p>
                <p>• 当前已添加 <span id="point-count">0</span> 个点</p>
            </div>
        `;
        document.body.appendChild(tips);
    }

    // 隐藏绘制提示
    hideDrawingTips() {
        const tips = document.getElementById('drawing-tips');
        if (tips) {
            tips.remove();
        }
    }

    // 开始飞行
    flyRoute(routeId) {
        // 设置飞行进度回调函数
        this.roamTool.onFlyProgress = (progressData) => {
            this.updateFlightProgress(progressData);
        };

        if (this.roamTool.startFly(routeId)) {
            this.switchMode('fly');
            this.updateFlightInfo();
        } else {
            this.showMessage('无法开始飞行，请检查路径数据', 'error');
        }
    }

    // 切换飞行状态
    toggleFly() {
        const isPaused = this.roamTool.pauseFly();
        const btn = document.getElementById('play-pause-btn');
        if (btn) {
            if (isPaused) {
                btn.innerHTML = '<i class="fa fa-play"></i> 继续';
            } else {
                btn.innerHTML = '<i class="fa fa-pause"></i> 暂停';
            }
        }
    }

    // 停止飞行
    stopFly() {
        this.roamTool.stopFly();
        this.switchMode('list');
        this.showMessage('飞行已停止', 'info');
    }

    // 设置飞行速度
    setSpeed(multiplier) {
        this.roamTool.setFlySpeed(multiplier);
        
        // 更新UI
        document.querySelectorAll('.speed-buttons .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
        
        const speedSpan = document.getElementById('flight-speed');
        if (speedSpan) {
            speedSpan.textContent = multiplier + 'x';
        }
    }

    // 更新飞行信息
    updateFlightInfo() {
        if (!this.roamTool.isFlying) return;

        // 设置进度更新回调（只设置一次）
        if (!this.roamTool.onFlyProgress) {
            this.roamTool.onFlyProgress = (progressData) => {
                this.updateFlightProgress(progressData);
            };
        }
    }

    // 更新飞行进度界面
    updateFlightProgress(progressData) {
        // 更新进度条
        const progressFill = document.getElementById('progress-fill');
        if (progressFill) {
            progressFill.style.width = `${(progressData.progress * 100).toFixed(1)}%`;
        }

        // 更新进度百分比
        const progressPercent = document.getElementById('progress-percent');
        if (progressPercent) {
            progressPercent.textContent = `${(progressData.progress * 100).toFixed(1)}%`;
        }

        // 更新剩余时间
        const remainingTime = document.getElementById('remaining-time');
        if (remainingTime) {
            const remaining = progressData.totalTime - progressData.elapsedTime;
            const minutes = Math.floor(remaining / 60);
            const seconds = Math.floor(remaining % 60);
            remainingTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // 更新当前高度
        const currentHeight = document.getElementById('current-height');
        if (currentHeight) {
            currentHeight.textContent = `${Math.round(progressData.currentHeight)} m`;
        }

        // 更新已飞行距离
        const flownDistance = document.getElementById('flown-distance');
        if (flownDistance) {
            const distance = progressData.distance;
            const distanceText = distance > 1000 ? 
                `${(distance / 1000).toFixed(2)}km` : 
                `${Math.round(distance)}m`;
            flownDistance.textContent = distanceText;
        }

        // 飞行完成处理
        if (progressData.progress >= 1.0) {
            this.onFlightComplete();
        }
    }

    // 飞行完成处理
    onFlightComplete() {
        this.showMessage('飞行完成！', 'success');
        // 可以在这里添加其他完成后的处理逻辑
    }

    // 显示图表
    async showChart(routeId) {
        this.switchMode('chart');
        
        try {
            const profileData = await this.roamTool.generateHeightProfile(routeId);
            if (profileData) {
                this.renderChart(profileData);
            }
        } catch (error) {
            console.error('生成高度剖面失败:', error);
            this.showMessage('生成高度剖面失败', 'error');
        }
    }

    // 渲染图表
    renderChart(profileData) {
        const chartContainer = document.getElementById('roam-chart');
        if (!chartContainer || !window.echarts) return;

        // 准备图表数据
        const xAxisData = profileData.points.map(p => (p.distance / 1000).toFixed(1)); // 转换为公里
        const heightData = profileData.points.map(p => p.height.toFixed(1));

        const option = {
            title: {
                text: profileData.routeName + ' - 高度剖面',
                left: 'center',
                textStyle: { fontSize: 14 }
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    const point = profileData.points[params[0].dataIndex];
                    return `
                        距离: ${(point.distance / 1000).toFixed(2)} km<br/>
                        高度: ${point.height.toFixed(1)} m<br/>
                        经度: ${point.longitude.toFixed(6)}°<br/>
                        纬度: ${point.latitude.toFixed(6)}°
                    `;
                }
            },
            xAxis: {
                type: 'category',
                data: xAxisData,
                name: '距离 (km)',
                nameLocation: 'middle',
                nameGap: 25
            },
            yAxis: {
                type: 'value',
                name: '高度 (m)',
                nameLocation: 'middle',
                nameGap: 40
            },
            series: [{
                data: heightData,
                type: 'line',
                smooth: true,
                areaStyle: {
                    opacity: 0.3
                },
                lineStyle: {
                    color: '#00ffff',
                    width: 2
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(0,255,255,0.3)' },
                        { offset: 1, color: 'rgba(0,255,255,0.1)' }
                    ])
                }
            }],
            grid: {
                left: '10%',
                right: '5%',
                top: '15%',
                bottom: '15%'
            }
        };

        if (this.chartInstance) {
            this.chartInstance.dispose();
        }
        
        this.chartInstance = echarts.init(chartContainer);
        this.chartInstance.setOption(option);

        // 更新统计信息
        const statsContainer = document.getElementById('chart-stats');
        if (statsContainer) {
            statsContainer.innerHTML = `
                <div class="stat-item">总距离: ${(profileData.totalDistance / 1000).toFixed(2)} km</div>
                <div class="stat-item">最高点: ${profileData.maxHeight.toFixed(1)} m</div>
                <div class="stat-item">最低点: ${profileData.minHeight.toFixed(1)} m</div>
                <div class="stat-item">高差: ${(profileData.maxHeight - profileData.minHeight).toFixed(1)} m</div>
            `;
        }
    }

    // 删除路径
    deleteRoute(routeId) {
        if (confirm('确定要删除这条路径吗？')) {
            if (this.roamTool.deleteRoute(routeId)) {
                this.updatePanel();
                this.showMessage('路径已删除', 'success');
            }
        }
    }

    // 导出路径
    exportRoute(routeId) {
        const data = this.roamTool.exportRoute(routeId);
        if (data) {
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `roam_route_${routeId}.json`;
            a.click();
            URL.revokeObjectURL(url);
            this.showMessage('路径已导出', 'success');
        }
    }

    // 导入路径
    importRoute() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const routeData = JSON.parse(e.target.result);
                        if (this.roamTool.importRoute(routeData)) {
                            this.updatePanel();
                            this.showMessage('路径导入成功', 'success');
                        } else {
                            this.showMessage('无效的路径文件', 'error');
                        }
                    } catch (error) {
                        this.showMessage('文件格式错误', 'error');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    // 设置事件监听
    setupEventListeners() {
        // 飞行进度监听已在updateFlightInfo中设置
    }

    // 设置面板事件
    setupPanelEvents() {
        // 拖拽功能
        const header = this.panel.querySelector('.panel-header h3');
        if (header) {
            let isDragging = false;
            let dragOffset = { x: 0, y: 0 };

            header.addEventListener('mousedown', (e) => {
                isDragging = true;
                dragOffset.x = e.clientX - this.panel.offsetLeft;
                dragOffset.y = e.clientY - this.panel.offsetTop;
                header.style.cursor = 'move';
            });

            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    this.panel.style.left = (e.clientX - dragOffset.x) + 'px';
                    this.panel.style.top = (e.clientY - dragOffset.y) + 'px';
                }
            });

            document.addEventListener('mouseup', () => {
                isDragging = false;
                header.style.cursor = 'pointer';
            });
        }

        this.setupContentEvents();
    }

    // 设置内容事件
    setupContentEvents() {
        // 内容相关的事件监听
    }

    // 显示消息
    showMessage(message, type = 'info') {
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        
        Object.assign(messageEl.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '10px 20px',
            borderRadius: '4px',
            zIndex: '10001',
            color: 'white',
            fontSize: '14px'
        });

        switch (type) {
            case 'success':
                messageEl.style.backgroundColor = '#4CAF50';
                break;
            case 'error':
                messageEl.style.backgroundColor = '#f44336';
                break;
            case 'warning':
                messageEl.style.backgroundColor = '#ff9800';
                break;
            default:
                messageEl.style.backgroundColor = '#2196F3';
        }

        document.body.appendChild(messageEl);

        setTimeout(() => {
            messageEl.remove();
        }, 3000);
    }

    // 关闭面板
    closePanel() {
        if (this.drawingHandler) {
            this.finishDrawing();
        }

        if (this.panel) {
            // 先移除show类，然后延迟删除面板以显示动画
            this.panel.classList.remove('show');
            setTimeout(() => {
                if (this.panel) {
                    this.panel.remove();
                    this.panel = null;
                }
            }, 300);
        }

        if (this.chartInstance) {
            this.chartInstance.dispose();
            this.chartInstance = null;
        }
    }

    // 显示/隐藏面板
    togglePanel() {
        if (this.panel) {
            this.closePanel();
        } else {
            this.createPanel();
        }
    }
}

// 导出到全局
window.RoamUI = RoamUI;