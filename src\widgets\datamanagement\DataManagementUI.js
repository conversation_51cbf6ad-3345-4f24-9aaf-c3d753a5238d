/**
 * 数据管理工具用户界面
 * 集成分享、打印和书签功能
 */
class DataManagementUI {
    constructor(viewer, shareTool, printTool, bookmarkTool) {
        this.viewer = viewer;
        this.shareTool = shareTool;
        this.printTool = printTool;
        this.bookmarkTool = bookmarkTool;
        this.panel = null;
        this.currentTab = 'share';
    }

    // 创建主面板
    createPanel() {
        if (this.panel) {
            this.panel.remove();
        }

        this.panel = document.createElement('div');
        this.panel.className = 'tool-panel';
        this.panel.innerHTML = `
            <div class="tool-panel-header">
                <div class="tool-panel-title">
                    <i class="fa fa-database"></i>
                    数据管理
                </div>
                <button class="tool-panel-close" onclick="dataManagementUI.closePanel()">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <div class="panel-tabs">
                <button class="tab-btn ${this.currentTab === 'share' ? 'active' : ''}" 
                        onclick="dataManagementUI.switchTab('share')">
                    <i class="fa fa-share-alt"></i> 分享
                </button>
                <button class="tab-btn ${this.currentTab === 'print' ? 'active' : ''}" 
                        onclick="dataManagementUI.switchTab('print')">
                    <i class="fa fa-print"></i> 打印
                </button>
                <button class="tab-btn ${this.currentTab === 'bookmark' ? 'active' : ''}" 
                        onclick="dataManagementUI.switchTab('bookmark')">
                    <i class="fa fa-bookmark"></i> 书签
                </button>
            </div>
            <div class="tool-panel-content">
                ${this.getTabContent()}
            </div>
        `;

        document.body.appendChild(this.panel);
        this.setupPanelEvents();
        return this.panel;
    }

    // 获取标签页内容
    getTabContent() {
        switch (this.currentTab) {
            case 'share':
                return this.getShareContent();
            case 'print':
                return this.getPrintContent();
            case 'bookmark':
                return this.getBookmarkContent();
            default:
                return '<div>未知标签页</div>';
        }
    }

    // 分享内容
    getShareContent() {
        return `
            <div class="share-container">
                <div class="section">
                    <h4><i class="fa fa-link"></i> 快速分享</h4>
                    <div class="quick-share">
                        <button class="btn btn-primary" onclick="dataManagementUI.generateShareUrl()">
                            <i class="fa fa-link"></i> 生成分享链接
                        </button>
                        <div class="share-url-container" id="share-url-container" style="display: none;">
                            <div class="form-group">
                                <label>分享链接:</label>
                                <div class="url-input-group">
                                    <input type="text" id="share-url-input" readonly>
                                    <button class="btn btn-sm" onclick="dataManagementUI.copyShareUrl()" title="复制链接">
                                        <i class="fa fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="share-options">
                                <label>
                                    <input type="checkbox" id="include-layers"> 包含图层状态
                                </label>
                                <label>
                                    <input type="checkbox" id="include-entities"> 包含标注数据
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h4><i class="fa fa-save"></i> 保存状态</h4>
                    <div class="save-state">
                        <div class="form-group">
                            <input type="text" id="state-name" placeholder="输入状态名称">
                        </div>
                        <button class="btn btn-primary" onclick="dataManagementUI.saveCurrentState()">
                            <i class="fa fa-save"></i> 保存当前状态
                        </button>
                    </div>
                    <div class="saved-states" id="saved-states">
                        ${this.getSavedStatesHTML()}
                    </div>
                </div>

                <div class="section">
                    <h4><i class="fa fa-exchange"></i> 导入导出</h4>
                    <div class="import-export">
                        <button class="btn btn-secondary" onclick="dataManagementUI.exportState()">
                            <i class="fa fa-download"></i> 导出状态
                        </button>
                        <button class="btn btn-secondary" onclick="dataManagementUI.importState()">
                            <i class="fa fa-upload"></i> 导入状态
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // 打印内容
    getPrintContent() {
        const presets = this.printTool.getPresetSizes();
        const formats = this.printTool.getSupportedFormats();
        
        return `
            <div class="print-container">
                <div class="section">
                    <h4><i class="fa fa-camera"></i> 截图设置</h4>
                    <form class="print-form" id="print-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label>格式:</label>
                                <select name="format">
                                    ${formats.map(fmt => `<option value="${fmt}">${fmt}</option>`).join('')}
                                </select>
                            </div>
                            <div class="form-group">
                                <label>质量:</label>
                                <input type="range" name="quality" min="0.1" max="1" step="0.1" value="1">
                                <span class="quality-value">100%</span>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>尺寸预设:</label>
                            <select name="size-preset" onchange="dataManagementUI.updateSizeFromPreset(this.value)">
                                <option value="">自定义</option>
                                ${Object.keys(presets).map(key => 
                                    `<option value="${key}">${key}</option>`
                                ).join('')}
                            </select>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label>宽度:</label>
                                <input type="number" name="width" placeholder="默认画布宽度">
                            </div>
                            <div class="form-group">
                                <label>高度:</label>
                                <input type="number" name="height" placeholder="默认画布高度">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>标题:</label>
                            <input type="text" name="title" placeholder="可选">
                        </div>
                        
                        <div class="form-group">
                            <label>副标题:</label>
                            <input type="text" name="subtitle" placeholder="可选">
                        </div>
                        
                        <div class="decoration-options">
                            <h5>装饰选项:</h5>
                            <div class="checkbox-grid">
                                <label><input type="checkbox" name="showScale" checked> 比例尺</label>
                                <label><input type="checkbox" name="showNorthArrow" checked> 指北针</label>
                                <label><input type="checkbox" name="showCoordinates" checked> 坐标信息</label>
                                <label><input type="checkbox" name="showTimestamp" checked> 时间戳</label>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="section">
                    <h4><i class="fa fa-download"></i> 操作</h4>
                    <div class="print-actions">
                        <button class="btn btn-primary" onclick="dataManagementUI.captureScreenshot()">
                            <i class="fa fa-camera"></i> 截图
                        </button>
                        <button class="btn btn-primary" onclick="dataManagementUI.downloadScreenshot()">
                            <i class="fa fa-download"></i> 下载截图
                        </button>
                        <button class="btn btn-secondary" onclick="dataManagementUI.printMap()">
                            <i class="fa fa-print"></i> 打印地图
                        </button>
                        <button class="btn btn-secondary" onclick="dataManagementUI.generatePDF()">
                            <i class="fa fa-file-pdf-o"></i> 生成PDF
                        </button>
                    </div>
                </div>

                <div class="section">
                    <h4><i class="fa fa-eye"></i> 预览</h4>
                    <div class="preview-container">
                        <div id="screenshot-preview" class="screenshot-preview">
                            <div class="preview-placeholder">
                                <i class="fa fa-image"></i>
                                <p>点击截图按钮生成预览</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 书签内容
    getBookmarkContent() {
        const bookmarks = this.bookmarkTool.getAllBookmarks();
        
        return `
            <div class="bookmark-container">
                <div class="section">
                    <h4><i class="fa fa-plus"></i> 创建书签</h4>
                    <div class="create-bookmark">
                        <div class="form-group">
                            <input type="text" id="bookmark-name" placeholder="书签名称">
                        </div>
                        <div class="form-group">
                            <textarea id="bookmark-description" placeholder="描述（可选）" rows="2"></textarea>
                        </div>
                        <button class="btn btn-primary" onclick="dataManagementUI.saveBookmark()">
                            <i class="fa fa-bookmark"></i> 保存当前视点
                        </button>
                    </div>
                </div>

                <div class="section">
                    <h4><i class="fa fa-list"></i> 书签列表</h4>
                    <div class="bookmark-list" id="bookmark-list">
                        ${this.getBookmarksHTML(bookmarks)}
                    </div>
                </div>

                <div class="section">
                    <h4><i class="fa fa-exchange"></i> 导入导出</h4>
                    <div class="bookmark-import-export">
                        <button class="btn btn-secondary" onclick="dataManagementUI.exportBookmarks()">
                            <i class="fa fa-download"></i> 导出书签
                        </button>
                        <button class="btn btn-secondary" onclick="dataManagementUI.importBookmarks()">
                            <i class="fa fa-upload"></i> 导入书签
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // 获取保存状态HTML
    getSavedStatesHTML() {
        const states = this.shareTool.getSavedStates();
        if (states.length === 0) {
            return '<div class="no-data">暂无保存的状态</div>';
        }
        
        return states.map((state, index) => `
            <div class="saved-state-item">
                <div class="state-info">
                    <div class="state-name">${state.name}</div>
                    <div class="state-time">${new Date(state.createTime).toLocaleString()}</div>
                </div>
                <div class="state-actions">
                    <button class="btn btn-sm" onclick="dataManagementUI.loadState(${index})" title="加载">
                        <i class="fa fa-play"></i>
                    </button>
                    <button class="btn btn-sm" onclick="dataManagementUI.shareState(${index})" title="分享">
                        <i class="fa fa-share"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="dataManagementUI.deleteState(${index})" title="删除">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 获取书签HTML
    getBookmarksHTML(bookmarks) {
        if (bookmarks.length === 0) {
            return '<div class="no-data">暂无书签，保存当前视点创建书签</div>';
        }
        
        return bookmarks.map(bookmark => `
            <div class="bookmark-item">
                <div class="bookmark-info">
                    <div class="bookmark-name">${bookmark.name}</div>
                    <div class="bookmark-detail">
                        ${bookmark.geographic.longitude}°, ${bookmark.geographic.latitude}°
                        ${bookmark.description ? `<br><span class="bookmark-desc">${bookmark.description}</span>` : ''}
                    </div>
                    <div class="bookmark-time">${new Date(bookmark.createTime).toLocaleString()}</div>
                </div>
                <div class="bookmark-actions">
                    <button class="btn btn-sm" onclick="dataManagementUI.flyToBookmark('${bookmark.id}')" title="飞到">
                        <i class="fa fa-location-arrow"></i>
                    </button>
                    <button class="btn btn-sm" onclick="dataManagementUI.editBookmark('${bookmark.id}')" title="编辑">
                        <i class="fa fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="dataManagementUI.deleteBookmark('${bookmark.id}')" title="删除">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 切换标签页
    switchTab(tab) {
        this.currentTab = tab;
        this.updatePanel();
    }

    // 更新面板
    updatePanel() {
        if (!this.panel) return;
        
        // 更新标签按钮状态
        const tabBtns = this.panel.querySelectorAll('.tab-btn');
        tabBtns.forEach(btn => btn.classList.remove('active'));
        this.panel.querySelector(`.tab-btn:nth-child(${this.getTabIndex()})`).classList.add('active');
        
        // 更新内容
        const content = this.panel.querySelector('.tool-panel-content');
        if (content) {
            content.innerHTML = this.getTabContent();
            this.setupContentEvents();
        }
    }

    // 获取标签索引
    getTabIndex() {
        const tabs = ['share', 'print', 'bookmark'];
        return tabs.indexOf(this.currentTab) + 1;
    }

    // 生成分享链接
    generateShareUrl() {
        const includeLayers = document.getElementById('include-layers')?.checked || false;
        const includeEntities = document.getElementById('include-entities')?.checked || false;
        
        const url = this.shareTool.generateShareUrl({
            includeLayers,
            includeEntities
        });
        
        const container = document.getElementById('share-url-container');
        const input = document.getElementById('share-url-input');
        
        if (container && input) {
            input.value = url;
            container.style.display = 'block';
        }
        
        this.showMessage('分享链接已生成', 'success');
    }

    // 复制分享链接
    async copyShareUrl() {
        const input = document.getElementById('share-url-input');
        if (input && input.value) {
            const success = await this.shareTool.copyToClipboard(input.value);
            if (success) {
                this.showMessage('链接已复制到剪贴板', 'success');
            } else {
                this.showMessage('复制失败，请手动复制', 'error');
            }
        }
    }

    // 保存当前状态
    saveCurrentState() {
        const nameInput = document.getElementById('state-name');
        const name = nameInput?.value.trim();
        
        if (!name) {
            this.showMessage('请输入状态名称', 'warning');
            return;
        }
        
        if (this.shareTool.saveStateLocally(name)) {
            nameInput.value = '';
            this.updateSavedStates();
            this.showMessage('状态保存成功', 'success');
        } else {
            this.showMessage('状态保存失败', 'error');
        }
    }

    // 更新保存状态列表
    updateSavedStates() {
        const container = document.getElementById('saved-states');
        if (container) {
            container.innerHTML = this.getSavedStatesHTML();
        }
    }

    // 加载状态
    loadState(index) {
        const states = this.shareTool.getSavedStates();
        if (states[index]) {
            if (this.shareTool.applyState(states[index].state)) {
                this.showMessage('状态加载成功', 'success');
            } else {
                this.showMessage('状态加载失败', 'error');
            }
        }
    }

    // 分享状态
    shareState(index) {
        const states = this.shareTool.getSavedStates();
        if (states[index]) {
            const url = this.shareTool.generateShareUrl();
            this.shareTool.copyToClipboard(url);
            this.showMessage('状态分享链接已复制', 'success');
        }
    }

    // 删除状态
    deleteState(index) {
        if (confirm('确定要删除这个保存的状态吗？')) {
            if (this.shareTool.deleteSavedState(index)) {
                this.updateSavedStates();
                this.showMessage('状态已删除', 'success');
            }
        }
    }

    // 导出状态
    exportState() {
        this.shareTool.exportState('json');
        this.showMessage('状态已导出', 'success');
    }

    // 导入状态
    importState() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json,.txt';
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (file) {
                try {
                    await this.shareTool.importState(file);
                    this.showMessage('状态导入成功', 'success');
                } catch (error) {
                    this.showMessage('状态导入失败: ' + error.message, 'error');
                }
            }
        };
        input.click();
    }

    // 截图
    async captureScreenshot() {
        const settings = this.getPrintSettings();
        try {
            const dataUrl = await this.printTool.captureScreen(settings);
            this.showScreenshotPreview(dataUrl);
            this.showMessage('截图成功', 'success');
        } catch (error) {
            this.showMessage('截图失败: ' + error.message, 'error');
        }
    }

    // 下载截图
    async downloadScreenshot() {
        const settings = this.getPrintSettings();
        const success = await this.printTool.downloadScreenshot(null, settings);
        if (success) {
            this.showMessage('截图已下载', 'success');
        } else {
            this.showMessage('下载失败', 'error');
        }
    }

    // 打印地图
    async printMap() {
        const settings = this.getPrintSettings();
        const success = await this.printTool.printMap(settings);
        if (success) {
            this.showMessage('打印任务已启动', 'success');
        } else {
            this.showMessage('打印失败', 'error');
        }
    }

    // 生成PDF
    async generatePDF() {
        const settings = this.getPrintSettings();
        const success = await this.printTool.generatePDF(settings);
        if (success) {
            this.showMessage('PDF已生成', 'success');
        } else {
            this.showMessage('PDF生成失败', 'error');
        }
    }

    // 获取打印设置
    getPrintSettings() {
        const form = document.getElementById('print-form');
        if (!form) return {};
        
        const formData = new FormData(form);
        return {
            format: formData.get('format'),
            quality: parseFloat(formData.get('quality')),
            width: parseInt(formData.get('width')) || null,
            height: parseInt(formData.get('height')) || null,
            title: formData.get('title'),
            subtitle: formData.get('subtitle'),
            showScale: formData.has('showScale'),
            showNorthArrow: formData.has('showNorthArrow'),
            showCoordinates: formData.has('showCoordinates'),
            showTimestamp: formData.has('showTimestamp')
        };
    }

    // 从预设更新尺寸
    updateSizeFromPreset(presetName) {
        if (!presetName) return;
        
        const presets = this.printTool.getPresetSizes();
        const preset = presets[presetName];
        
        if (preset) {
            const widthInput = document.querySelector('input[name="width"]');
            const heightInput = document.querySelector('input[name="height"]');
            
            if (widthInput) widthInput.value = preset.width;
            if (heightInput) heightInput.value = preset.height;
        }
    }

    // 显示截图预览
    showScreenshotPreview(dataUrl) {
        const preview = document.getElementById('screenshot-preview');
        if (preview) {
            preview.innerHTML = `<img src="${dataUrl}" alt="截图预览" style="max-width: 100%; height: auto;">`;
        }
    }

    // 保存书签
    saveBookmark() {
        const nameInput = document.getElementById('bookmark-name');
        const descInput = document.getElementById('bookmark-description');
        
        const name = nameInput?.value.trim();
        const description = descInput?.value.trim() || '';
        
        if (!name) {
            this.showMessage('请输入书签名称', 'warning');
            return;
        }
        
        this.bookmarkTool.saveCurrentView(name, description);
        
        if (nameInput) nameInput.value = '';
        if (descInput) descInput.value = '';
        
        this.updateBookmarksList();
        this.showMessage('书签保存成功', 'success');
    }

    // 更新书签列表
    updateBookmarksList() {
        const container = document.getElementById('bookmark-list');
        if (container) {
            const bookmarks = this.bookmarkTool.getAllBookmarks();
            container.innerHTML = this.getBookmarksHTML(bookmarks);
        }
    }

    // 飞到书签
    flyToBookmark(bookmarkId) {
        this.bookmarkTool.flyToBookmark(bookmarkId);
        this.showMessage('正在飞往书签位置', 'info');
    }

    // 编辑书签
    editBookmark(bookmarkId) {
        const bookmark = this.bookmarkTool.getBookmarkById(bookmarkId);
        if (bookmark) {
            const newName = prompt('请输入新的书签名称:', bookmark.name);
            if (newName && newName.trim()) {
                bookmark.name = newName.trim();
                this.bookmarkTool.saveBookmarks();
                this.updateBookmarksList();
                this.showMessage('书签已更新', 'success');
            }
        }
    }

    // 删除书签
    deleteBookmark(bookmarkId) {
        if (confirm('确定要删除这个书签吗？')) {
            if (this.bookmarkTool.deleteBookmark(bookmarkId)) {
                this.updateBookmarksList();
                this.showMessage('书签已删除', 'success');
            }
        }
    }

    // 导出书签
    exportBookmarks() {
        this.bookmarkTool.exportBookmarks();
        this.showMessage('书签已导出', 'success');
    }

    // 导入书签
    importBookmarks() {
        this.bookmarkTool.importBookmarks((success) => {
            if (success) {
                this.updateBookmarksList();
                this.showMessage('书签导入成功', 'success');
            } else {
                this.showMessage('书签导入失败', 'error');
            }
        });
    }

    // 设置面板事件
    setupPanelEvents() {
        // 拖拽功能
        const header = this.panel.querySelector('.panel-header h3');
        if (header) {
            let isDragging = false;
            let dragOffset = { x: 0, y: 0 };

            header.addEventListener('mousedown', (e) => {
                isDragging = true;
                dragOffset.x = e.clientX - this.panel.offsetLeft;
                dragOffset.y = e.clientY - this.panel.offsetTop;
                header.style.cursor = 'move';
            });

            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    this.panel.style.left = (e.clientX - dragOffset.x) + 'px';
                    this.panel.style.top = (e.clientY - dragOffset.y) + 'px';
                }
            });

            document.addEventListener('mouseup', () => {
                isDragging = false;
                header.style.cursor = 'pointer';
            });
        }

        this.setupContentEvents();
    }

    // 设置内容事件
    setupContentEvents() {
        // 质量滑块事件
        const qualitySlider = this.panel.querySelector('input[name="quality"]');
        const qualityValue = this.panel.querySelector('.quality-value');
        
        if (qualitySlider && qualityValue) {
            qualitySlider.addEventListener('input', (e) => {
                qualityValue.textContent = Math.round(e.target.value * 100) + '%';
            });
        }
    }

    // 创建模态框
    createModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h4>${title}</h4>
                    <button class="btn btn-sm" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        `;
        return modal;
    }

    // 显示消息
    showMessage(message, type = 'info') {
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        
        Object.assign(messageEl.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '10px 20px',
            borderRadius: '4px',
            zIndex: '10001',
            color: 'white',
            fontSize: '14px'
        });

        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            warning: '#ff9800',
            info: '#2196F3'
        };

        messageEl.style.backgroundColor = colors[type] || colors.info;
        document.body.appendChild(messageEl);

        setTimeout(() => {
            messageEl.remove();
        }, 3000);
    }

    // 关闭面板
    closePanel() {
        if (this.panel) {
            this.panel.remove();
            this.panel = null;
        }
    }

    // 显示/隐藏面板
    togglePanel() {
        if (this.panel) {
            this.closePanel();
        } else {
            this.createPanel();
        }
    }
}

// 导出到全局
window.DataManagementUI = DataManagementUI;