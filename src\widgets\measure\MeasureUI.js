/**
 * 测量工具UI界面
 * 移植自widgets/measure界面设计
 */
class MeasureUI {
    constructor(viewer, containerId) {
        this.viewer = viewer;
        this.containerId = containerId;
        this.measureTool = new MeasureTool(viewer);
        this.panel = null;
        this.isVisible = false;
        
        this.init();
    }

    init() {
        this.createPanel();
        this.bindEvents();
    }

    createPanel() {
        // 创建面板HTML - 使用统一的工具面板样式
        const panelHtml = `
            <div id="measurePanel" class="tool-panel">
                <div class="tool-panel-header">
                    <div class="tool-panel-title">
                        <i class="fa fa-ruler"></i>
                        测量工具
                    </div>
                    <button class="tool-panel-close" onclick="measureUI.hide()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="tool-panel-content">
                    <div class="measure-toolbar">
                        <div class="measure-group">
                            <label>距离测量</label>
                            <div class="measure-buttons">
                                <button id="btnMeasureDistance" class="cesium-button" title="空间距离">
                                    <i class="fa fa-arrows-h"></i> 空间距离
                                </button>
                                <button id="btnMeasureDistanceGround" class="cesium-button" title="贴地距离">
                                    <i class="fa fa-road"></i> 贴地距离
                                </button>
                            </div>
                        </div>
                        
                        <div class="measure-group">
                            <label>面积测量</label>
                            <div class="measure-buttons">
                                <button id="btnMeasureArea" class="cesium-button" title="投影面积">
                                    <i class="fa fa-square-o"></i> 投影面积
                                </button>
                                <button id="btnMeasureAreaGround" class="cesium-button" title="贴地面积">
                                    <i class="fa fa-square"></i> 贴地面积
                                </button>
                            </div>
                        </div>
                        
                        <div class="measure-group">
                            <label>高度测量</label>
                            <div class="measure-buttons">
                                <button id="btnMeasureHeight" class="cesium-button" title="高度差">
                                    <i class="fa fa-arrows-v"></i> 高度差
                                </button>
                                <button id="btnMeasureTriangle" class="cesium-button" title="三角测量">
                                    <i class="fa fa-play"></i> 三角测量
                                </button>
                            </div>
                        </div>
                        
                        <div class="measure-group">
                            <label>角度测量</label>
                            <div class="measure-buttons">
                                <button id="btnMeasureAngle" class="cesium-button" title="角度测量">
                                    <i class="fa fa-angle-right"></i> 角度测量
                                </button>
                                <button id="btnMeasureCoordinate" class="cesium-button" title="坐标测量">
                                    <i class="fa fa-crosshairs"></i> 坐标测量
                                </button>
                            </div>
                        </div>
                        
                        <div class="measure-group">
                            <label>操作</label>
                            <div class="measure-buttons">
                                <button id="btnClearMeasure" class="cesium-button cesium-button-danger" title="清除">
                                    <i class="fa fa-trash"></i> 清除
                                </button>
                                <button id="btnCloseMeasure" class="cesium-button" title="关闭">
                                    <i class="fa fa-times"></i> 关闭
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="measure-results">
                        <div class="measure-result-header">
                            <span>测量结果</span>
                            <button id="btnClearResults" class="clear-results-btn">清空</button>
                        </div>
                        <div id="measureResults" class="measure-result-list">
                            <!-- 测量结果将显示在这里 -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', panelHtml);
        this.panel = document.getElementById('measurePanel');
    }

    bindEvents() {
        // 距离测量
        document.getElementById('btnMeasureDistance').addEventListener('click', () => {
            this.startMeasureDistance(false);
        });

        document.getElementById('btnMeasureDistanceGround').addEventListener('click', () => {
            this.startMeasureDistance(true);
        });

        // 面积测量
        document.getElementById('btnMeasureArea').addEventListener('click', () => {
            this.startMeasureArea(false);
        });

        document.getElementById('btnMeasureAreaGround').addEventListener('click', () => {
            this.startMeasureArea(true);
        });

        // 高度测量
        document.getElementById('btnMeasureHeight').addEventListener('click', () => {
            this.startMeasureHeight();
        });

        document.getElementById('btnMeasureTriangle').addEventListener('click', () => {
            this.startMeasureTriangle();
        });

        // 角度测量
        document.getElementById('btnMeasureAngle').addEventListener('click', () => {
            this.startMeasureAngle();
        });

        document.getElementById('btnMeasureCoordinate').addEventListener('click', () => {
            this.startMeasureCoordinate();
        });

        // 操作按钮
        document.getElementById('btnClearMeasure').addEventListener('click', () => {
            this.clearAll();
        });

        document.getElementById('btnCloseMeasure').addEventListener('click', () => {
            this.hide();
        });

        document.getElementById('btnClearResults').addEventListener('click', () => {
            this.clearResults();
        });
    }

    // 显示面板
    show() {
        if (this.panel) {
            this.panel.classList.add('show');
            this.isVisible = true;
        }
    }

    // 隐藏面板
    hide() {
        if (this.panel) {
            this.panel.classList.remove('show');
            this.isVisible = false;
            this.measureTool.deactivate();
        }
    }

    // 切换显示状态
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    // 开始距离测量
    startMeasureDistance(clampToGround = false) {
        this.setActiveButton('btnMeasureDistance' + (clampToGround ? 'Ground' : ''));
        this.showTip(clampToGround ? '单击开始绘制贴地距离，右键结束' : '单击开始绘制空间距离，右键结束');
        
        this.measureTool.measureDistance({
            clampToGround: clampToGround,
            callback: (distance) => {
                this.addResult('距离测量', this.measureTool.formatDistance(distance), clampToGround ? '贴地' : '空间');
                this.clearActiveButton();
                this.hideTip();
            }
        });
    }

    // 开始面积测量
    startMeasureArea(clampToGround = false) {
        this.setActiveButton('btnMeasureArea' + (clampToGround ? 'Ground' : ''));
        this.showTip(clampToGround ? '单击开始绘制贴地面积，右键结束' : '单击开始绘制投影面积，右键结束');
        
        this.measureTool.measureArea({
            clampToGround: clampToGround,
            callback: (area) => {
                this.addResult('面积测量', this.measureTool.formatArea(area), clampToGround ? '贴地' : '投影');
                this.clearActiveButton();
                this.hideTip();
            }
        });
    }

    // 开始高度测量
    startMeasureHeight() {
        this.setActiveButton('btnMeasureHeight');
        this.showTip('单击选择起点，再单击选择终点');
        
        this.measureTool.measureHeight({
            callback: (height) => {
                this.addResult('高度测量', this.measureTool.formatDistance(height, 'm'), '高度差');
                this.clearActiveButton();
                this.hideTip();
            }
        });
    }

    // 开始三角测量
    startMeasureTriangle() {
        this.setActiveButton('btnMeasureTriangle');
        this.showTip('三角测量功能开发中...');
        // TODO: 实现三角测量
        setTimeout(() => {
            this.clearActiveButton();
            this.hideTip();
        }, 2000);
    }

    // 开始角度测量
    startMeasureAngle() {
        this.setActiveButton('btnMeasureAngle');
        this.showTip('角度测量功能开发中...');
        // TODO: 实现角度测量
        setTimeout(() => {
            this.clearActiveButton();
            this.hideTip();
        }, 2000);
    }

    // 开始坐标测量
    startMeasureCoordinate() {
        this.setActiveButton('btnMeasureCoordinate');
        this.showTip('单击地图获取坐标信息');
        
        // 简单的坐标获取实现
        const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        handler.setInputAction((event) => {
            const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
            if (position) {
                const cartographic = Cesium.Cartographic.fromCartesian(position);
                const longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
                const latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);
                const height = cartographic.height.toFixed(2);
                
                this.addResult('坐标测量', `经度: ${longitude}°, 纬度: ${latitude}°, 高度: ${height}m`, '地理坐标');
                
                // 添加坐标标记
                this.measureTool.addPointMarker(position, '坐标', `${longitude}°, ${latitude}°`);
            }
            
            handler.destroy();
            this.clearActiveButton();
            this.hideTip();
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

    // 设置活动按钮
    setActiveButton(buttonId) {
        this.clearActiveButton();
        const button = document.getElementById(buttonId);
        if (button) {
            button.classList.add('active');
        }
    }

    // 清除活动按钮
    clearActiveButton() {
        const activeButtons = this.panel.querySelectorAll('.cesium-button.active');
        activeButtons.forEach(btn => btn.classList.remove('active'));
    }

    // 显示提示
    showTip(message) {
        // 可以在这里实现提示信息显示
        console.log('测量提示:', message);
    }

    // 隐藏提示
    hideTip() {
        // 隐藏提示信息
    }

    // 添加测量结果
    addResult(type, value, subType = '') {
        const resultsContainer = document.getElementById('measureResults');
        const resultItem = document.createElement('div');
        resultItem.className = 'measure-result-item';
        
        const time = new Date().toLocaleTimeString();
        resultItem.innerHTML = `
            <div class="result-header">
                <span class="result-type">${type}${subType ? ` (${subType})` : ''}</span>
                <span class="result-time">${time}</span>
            </div>
            <div class="result-value">${value}</div>
        `;
        
        resultsContainer.appendChild(resultItem);
        resultsContainer.scrollTop = resultsContainer.scrollHeight;
    }

    // 清除所有测量结果
    clearAll() {
        this.measureTool.clearAll();
        this.clearResults();
        this.clearActiveButton();
        this.hideTip();
    }

    // 清除结果列表
    clearResults() {
        const resultsContainer = document.getElementById('measureResults');
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
        }
    }

    // 销毁
    destroy() {
        if (this.measureTool) {
            this.measureTool.destroy();
        }
        if (this.panel) {
            this.panel.remove();
        }
    }
}

// 导出
window.MeasureUI = MeasureUI;
