// 坐标显示类
// 注意：CSS文件已在HTML中通过link标签引入

// SVG图标内联定义 - 避免HTTP请求和路径问题
const SVG_ICONS = {
    altitude: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 2L14 12H2L8 2Z" fill="#4A90E2" stroke="#2E5C8A" stroke-width="1"/>
        <line x1="8" y1="2" x2="8" y2="14" stroke="#F5A623" stroke-width="1.5" stroke-dasharray="1,1"/>
        <line x1="1" y1="12" x2="15" y2="12" stroke="#7ED321" stroke-width="1.5"/>
        <circle cx="8" cy="2" r="1.5" fill="#F5A623" stroke="#E8940B" stroke-width="1"/>
        <circle cx="8" cy="12" r="1" fill="#7ED321" stroke="#5BA016" stroke-width="0.5"/>
        <rect x="9" y="6" width="5" height="2" fill="#FFFFFF" stroke="#2E5C8A" stroke-width="0.5" rx="0.5"/>
        <text x="11.5" y="7.5" text-anchor="middle" font-family="Arial" font-size="4" fill="#2E5C8A" font-weight="bold">Alt</text>
        <polygon points="6,4 8,2 10,4" fill="#7ED321" stroke="#5BA016" stroke-width="0.5"/>
    </svg>`,
    
    scale: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="6" width="14" height="4" fill="#4A90E2" stroke="#2E5C8A" stroke-width="1" rx="1"/>
        <line x1="1" y1="5" x2="1" y2="11" stroke="#F5A623" stroke-width="1.5"/>
        <line x1="5" y1="6" x2="5" y2="10" stroke="#7ED321" stroke-width="1"/>
        <line x1="8" y1="5" x2="8" y2="11" stroke="#F5A623" stroke-width="1.5"/>
        <line x1="11" y1="6" x2="11" y2="10" stroke="#7ED321" stroke-width="1"/>
        <line x1="15" y1="5" x2="15" y2="11" stroke="#F5A623" stroke-width="1.5"/>
        <line x1="1" y1="8" x2="15" y2="8" stroke="#FFFFFF" stroke-width="1.5"/>
        <rect x="2" y="7" width="3" height="2" fill="#FFFFFF" opacity="0.9" rx="0.3"/>
        <text x="3.5" y="8.5" text-anchor="middle" font-family="Arial" font-size="4" fill="#2E5C8A" font-weight="bold">1k</text>
        <rect x="11.5" y="7" width="3" height="2" fill="#FFFFFF" opacity="0.9" rx="0.3"/>
        <text x="13" y="8.5" text-anchor="middle" font-family="Arial" font-size="4" fill="#2E5C8A" font-weight="bold">2k</text>
        <circle cx="8" cy="3" r="1.5" fill="#F5A623" stroke="#E8940B" stroke-width="1"/>
    </svg>`,
    
    longitude: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="7" fill="#4A90E2" stroke="#2E5C8A" stroke-width="1"/>
        <ellipse cx="8" cy="8" rx="2" ry="6" fill="none" stroke="#7ED321" stroke-width="1.2"/>
        <ellipse cx="8" cy="8" rx="4" ry="6.5" fill="none" stroke="#7ED321" stroke-width="1"/>
        <ellipse cx="8" cy="8" rx="6" ry="6.8" fill="none" stroke="#7ED321" stroke-width="0.8"/>
        <line x1="8" y1="1" x2="8" y2="15" stroke="#F5A623" stroke-width="1.5"/>
        <circle cx="2" cy="8" r="1" fill="#F5A623"/>
        <circle cx="14" cy="8" r="1" fill="#F5A623"/>
        <text x="4" y="4" font-family="Arial" font-size="4" fill="#FFFFFF" font-weight="bold">W</text>
        <text x="12" y="4" font-family="Arial" font-size="4" fill="#FFFFFF" font-weight="bold">E</text>
    </svg>`,
    
    latitude: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="7" fill="#4A90E2" stroke="#2E5C8A" stroke-width="1"/>
        <ellipse cx="8" cy="8" rx="6" ry="2" fill="none" stroke="#7ED321" stroke-width="1.2"/>
        <ellipse cx="8" cy="8" rx="5" ry="4" fill="none" stroke="#7ED321" stroke-width="1"/>
        <ellipse cx="8" cy="8" rx="3" ry="6" fill="none" stroke="#7ED321" stroke-width="0.8"/>
        <line x1="1" y1="8" x2="15" y2="8" stroke="#F5A623" stroke-width="1.5"/>
        <circle cx="8" cy="2" r="1" fill="#F5A623"/>
        <circle cx="8" cy="14" r="1" fill="#F5A623"/>
        <text x="2" y="5" font-family="Arial" font-size="4" fill="#FFFFFF" font-weight="bold">N</text>
        <text x="2" y="12" font-family="Arial" font-size="4" fill="#FFFFFF" font-weight="bold">S</text>
    </svg>`
};

class CoordinateDisplay {
    constructor(viewer) {
        this.viewer = viewer;
        this.handler = null;
        this.coordinateSystem = 'WGS84'; // 默认坐标系
        this.container = null;
        this.distanceScale = null; // 比例尺值
        this.distanceBarWidth = 0; // 比例尺宽度
        
        this.initUI();
        this.initEventHandler();
        // SVG图标现在直接内联在HTML中，无需额外加载
    }

    // 初始化UI
    initUI() {
        // 创建容器
        this.container = document.createElement('div');
        this.container.className = 'coordinate-display';
        this.container.innerHTML = `
            <div class="coordinate-content">
                <div class="coordinate-text">
                    <span data-label="lon">--</span>
                    <span data-label="lat">--</span>
                    <span data-label="alt">--</span>
                </div>
                
                <!-- 比例尺显示 -->
                <div class="scale-display">
                    <div class="scale-bar"></div>
                    <div class="scale-text">--</div>
                </div>
                
                <select class="coordinate-system">
                    <option value="WGS84">WGS84</option>
                    <option value="DMS">度分秒</option>
                    <option value="Mercator">墨卡托</option>
                    <option value="UTM">UTM</option>
                </select>
            </div>
        `;

        document.body.appendChild(this.container);

        // 监听坐标系切换
        this.container.querySelector('.coordinate-system').addEventListener('change', (e) => {
            this.coordinateSystem = e.target.value;
        });
    }

    // 初始化事件处理
    initEventHandler() {
        this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        this.handler.setInputAction(this.onMouseMove.bind(this), Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        
        // 添加相机移动事件监听，用于更新比例尺
        this.viewer.camera.moveEnd.addEventListener(() => {
            this.updateDistanceScale();
        });
        
        // 初始化时更新一次比例尺
        this.updateDistanceScale();
    }

    // 更新比例尺
    updateDistanceScale() {
        // 获取当前视图的比例尺信息
        const canvas = this.viewer.scene.canvas;
        const width = canvas.clientWidth;
        const height = canvas.clientHeight;
        
        // 获取屏幕底部中心两个点的位置
        const leftPoint = new Cesium.Cartesian2(width / 2 - 100, height - 10);
        const rightPoint = new Cesium.Cartesian2(width / 2 + 100, height - 10);
        
        // 将屏幕坐标转换为地理坐标
        const leftRay = this.viewer.camera.getPickRay(leftPoint);
        const rightRay = this.viewer.camera.getPickRay(rightPoint);
        
        // 获取交点
        const leftCartesian = this.viewer.scene.globe.pick(leftRay, this.viewer.scene);
        const rightCartesian = this.viewer.scene.globe.pick(rightRay, this.viewer.scene);
        
        // 如果没有交点，无法计算距离
        if (!leftCartesian || !rightCartesian) {
            this.container.querySelector('.scale-text').textContent = '-- km';
            this.container.querySelector('.scale-bar').style.width = '60px';
            return;
        }
        
        // 计算两点间距离
        const distance = Cesium.Cartesian3.distance(leftCartesian, rightCartesian);
        
        // 根据距离选择合适的单位和比例
        let scaleDistance, unit;
        if (distance >= 1000) {
            scaleDistance = Math.round(distance / 100) / 10;
            unit = 'km';
        } else {
            scaleDistance = Math.round(distance);
            unit = 'm';
        }
        
        // 根据实际距离调整比例尺显示宽度
        const scaleWidth = 60; // 默认宽度
        
        // 更新UI
        this.container.querySelector('.scale-text').textContent = `${scaleDistance} ${unit}`;
        this.container.querySelector('.scale-bar').style.width = `${scaleWidth}px`;
    }

    // 鼠标移动事件处理
    onMouseMove(movement) {
        const cartesian = this.viewer.camera.pickEllipsoid(
            movement.endPosition,
            this.viewer.scene.globe.ellipsoid
        );

        if (cartesian) {
            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            const height = this.viewer.scene.globe.getHeight(cartographic) || 0;
            
            let coordText = '';
            switch (this.coordinateSystem) {
                case 'WGS84':
                    coordText = this.formatWGS84(cartographic, height);
                    break;
                case 'DMS':
                    coordText = this.formatDMS(cartographic, height);
                    break;
                case 'Mercator':
                    coordText = this.formatMercator(cartographic, height);
                    break;
                case 'UTM':
                    coordText = this.formatUTM(cartographic, height);
                    break;
            }

            this.container.querySelector('.coordinate-text').innerHTML = coordText;
            // SVG图标已直接内联在HTML中，无需重新加载
            // this.loadSvgIcons();
        }
    }

    // WGS84 格式化
    formatWGS84(cartographic, height) {
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        return `
            <span data-label="lon">${longitude.toFixed(4)}°</span>
            <span data-label="lat">${latitude.toFixed(4)}°</span>
            <span data-label="alt">${Math.round(height)}m</span>
        `;
    }

    // 度分秒格式化
    formatDMS(cartographic, height) {
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);

        const formatDMS = (decimal) => {
            const degrees = Math.floor(Math.abs(decimal));
            const minutes = Math.floor((Math.abs(decimal) - degrees) * 60);
            const seconds = ((Math.abs(decimal) - degrees - minutes/60) * 3600).toFixed(0);
            const direction = decimal >= 0 ? 'N' : 'S';
            return `${degrees}°${minutes}'${seconds}"${direction}`;
        };

        return `
            <span data-label="lon">${formatDMS(longitude)}</span>
            <span data-label="lat">${formatDMS(latitude)}</span>
            <span data-label="alt">${Math.round(height)}m</span>
        `;
    }

    // 墨卡托格式化
    formatMercator(cartographic, height) {
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        
        // 墨卡托投影转换
        const x = longitude * 20037508.34 / 180;
        const y = Math.log(Math.tan((90 + latitude) * Math.PI / 360)) / (Math.PI / 180);
        const mercatorY = y * 20037508.34 / 180;

        return `
            <span data-label="x">${Math.round(x)}</span>
            <span data-label="y">${Math.round(mercatorY)}</span>
            <span data-label="alt">${Math.round(height)}m</span>
        `;
    }

    // UTM格式化
    formatUTM(cartographic, height) {
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);

        // UTM区带计算
        const zone = Math.floor((longitude + 180) / 6) + 1;
        const letter = 'CDEFGHJKLMNPQRSTUVWXX'[Math.floor((latitude + 80) / 8)];

        // 简单的UTM坐标计算（这是一个简化版本）
        const centralMeridian = zone * 6 - 183;
        const easting = (longitude - centralMeridian) * 111319.9;
        const northing = latitude * 111319.9;

        return `
            <span data-label="utm">${zone}${letter}</span>
            <span data-label="e">${Math.round(easting)}</span>
            <span data-label="n">${Math.round(northing)}</span>
        `;
    }

    // 销毁
    destroy() {
        if (this.handler) {
            this.handler.destroy();
            this.handler = null;
        }
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
    }
}

// 导出类 - 使用全局变量方式
window.CoordinateDisplay = CoordinateDisplay; 