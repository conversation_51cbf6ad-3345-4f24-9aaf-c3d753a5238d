/* 漫游工具样式 */
.roam-panel {
    position: fixed;
    top: 80px;
    right: 20px;
    width: 350px;
    max-height: 80vh;
    overflow-y: auto;
    background: rgba(42, 42, 42, 0.9);
    border: 1px solid #555;
    border-radius: 8px;
    color: #fff;
    font-size: 13px;
    z-index: 9999;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.roam-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid #555;
    border-radius: 8px 8px 0 0;
}

.roam-panel .panel-header h3 {
    margin: 0;
    font-size: 14px;
    color: #00ffff;
    cursor: pointer;
    user-select: none;
}

.roam-panel .header-buttons {
    display: flex;
    gap: 4px;
}

.roam-panel .header-buttons .btn {
    padding: 4px 8px;
    min-width: auto;
}

.roam-panel .panel-content {
    padding: 16px;
}

/* 路径列表 */
.route-list-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.list-actions {
    display: flex;
    gap: 8px;
}

.route-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 400px;
    overflow-y: auto;
}

.route-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.route-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: #00ffff;
}

.route-info {
    flex: 1;
    min-width: 0;
}

.route-name {
    font-weight: bold;
    color: #00ffff;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.route-detail {
    font-size: 11px;
    color: #aaa;
    margin-bottom: 2px;
}

.route-desc {
    font-size: 11px;
    color: #ccc;
    font-style: italic;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.route-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
}

.route-actions .btn {
    padding: 4px 6px;
    min-width: auto;
    font-size: 11px;
}

.no-data {
    text-align: center;
    padding: 30px 20px;
    color: #888;
    font-style: italic;
}

/* 创建路径表单 */
.create-route-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.route-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.form-row {
    display: flex;
    gap: 12px;
}

.form-row .form-group {
    flex: 1;
}

.route-form label {
    font-size: 12px;
    color: #ccc;
    font-weight: normal;
}

.route-form input,
.route-form textarea {
    padding: 6px 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #555;
    border-radius: 3px;
    color: #fff;
    font-size: 12px;
}

.route-form input:focus,
.route-form textarea:focus {
    outline: none;
    border-color: #00ffff;
    background: rgba(255, 255, 255, 0.15);
}

.route-form input[type="checkbox"] {
    width: auto;
    margin-right: 6px;
}

.form-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.drawing-tips {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 4px;
    padding: 12px;
}

.drawing-tips h4 {
    margin: 0 0 8px 0;
    color: #00ffff;
    font-size: 12px;
}

.drawing-tips p {
    margin: 4px 0;
    font-size: 11px;
    color: #ccc;
}

/* 飞行控制 */
.fly-control-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.route-info-header h4 {
    margin: 0 0 8px 0;
    color: #00ffff;
    font-size: 14px;
}

.route-stats {
    display: flex;
    gap: 16px;
    font-size: 11px;
    color: #aaa;
}

.flight-progress {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ffff, #0080ff);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: #aaa;
}

.flight-info {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    font-size: 12px;
}

.info-item label {
    color: #ccc;
}

.info-item span {
    color: #00ffff;
    font-weight: bold;
}

.flight-controls {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.speed-control {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
}

.speed-control label {
    font-size: 12px;
    color: #ccc;
}

.speed-buttons {
    display: flex;
    gap: 4px;
}

.speed-buttons .btn {
    padding: 4px 8px;
    min-width: auto;
    font-size: 11px;
}

.speed-buttons .btn.active {
    background: #00ffff;
    color: #000;
}

/* 图表 */
.chart-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h4 {
    margin: 0;
    color: #00ffff;
    font-size: 14px;
}

.chart-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

#roam-chart {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    border: 1px solid #555;
}

.chart-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 8px;
    border-radius: 3px;
    text-align: center;
    font-size: 11px;
    color: #ccc;
}

/* 绘制提示覆盖层 */
.drawing-tips-overlay {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #00ffff;
    border-radius: 8px;
    padding: 20px;
    z-index: 10000;
    color: #fff;
    text-align: center;
}

.drawing-tips-overlay h4 {
    margin: 0 0 12px 0;
    color: #00ffff;
}

.drawing-tips-overlay p {
    margin: 6px 0;
    font-size: 13px;
}

/* 消息提示 */
.message {
    padding: 10px 20px;
    border-radius: 4px;
    margin-bottom: 10px;
    font-size: 13px;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.message-info {
    background: #2196F3;
    color: white;
}

.message-success {
    background: #4CAF50;
    color: white;
}

.message-warning {
    background: #ff9800;
    color: white;
}

.message-error {
    background: #f44336;
    color: white;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .roam-panel {
        width: 320px;
        right: 10px;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .chart-stats {
        grid-template-columns: 1fr;
    }
}

/* 滚动条样式 */
.roam-panel::-webkit-scrollbar,
.route-list::-webkit-scrollbar {
    width: 6px;
}

.roam-panel::-webkit-scrollbar-track,
.route-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.roam-panel::-webkit-scrollbar-thumb,
.route-list::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 255, 0.5);
    border-radius: 3px;
}

.roam-panel::-webkit-scrollbar-thumb:hover,
.route-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 255, 0.7);
}