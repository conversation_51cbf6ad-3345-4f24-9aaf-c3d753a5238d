/**
 * 漫游工具 - 基于原生Cesium实现
 * 集成路径飞行、路径管理和高度剖面功能
 */
class RoamTool {
    constructor(viewer) {
        this.viewer = viewer;
        this.routes = [];
        this.currentRoute = null;
        this.isFlying = false;
        this.flyEntity = null;
        this.pathEntity = null;
        this.clock = null;
        this.storageKey = 'cesium_roam_routes';
        
        this.loadRoutes();
        this.initClock();
    }

    // 初始化时钟控制
    initClock() {
        this.clock = this.viewer.clock;
        this.originalClockSettings = {
            startTime: this.clock.startTime.clone(),
            stopTime: this.clock.stopTime.clone(),
            currentTime: this.clock.currentTime.clone(),
            multiplier: this.clock.multiplier,
            shouldAnimate: this.clock.shouldAnimate
        };
    }

    // 创建新路径
    createRoute(name, description = '') {
        const route = {
            id: this.generateId(),
            name: name || `路径_${this.routes.length + 1}`,
            description: description,
            points: [],
            createTime: new Date().toISOString(),
            duration: 60, // 默认飞行时间(秒)
            height: 1000, // 默认飞行高度
            clampToGround: false,
            showPath: true,
            pathColor: '#00ffff'
        };
        
        this.routes.push(route);
        this.saveRoutes();
        return route;
    }

    // 添加路径点
    addRoutePoint(routeId, position, time) {
        const route = this.getRouteById(routeId);
        if (!route) return false;

        const point = {
            position: {
                x: position.x,
                y: position.y,
                z: position.z
            },
            time: time || route.points.length,
            height: 0 // 将异步计算高度
        };

        route.points.push(point);
        
        // 异步计算地面高度
        this.calculateGroundHeight(point);
        
        this.saveRoutes();
        return point;
    }

    // 计算地面高度
    async calculateGroundHeight(point) {
        const cartographic = Cesium.Cartographic.fromCartesian(
            new Cesium.Cartesian3(point.position.x, point.position.y, point.position.z)
        );
        
        try {
            const terrainProvider = this.viewer.terrainProvider;
            const heights = await Cesium.sampleTerrain(terrainProvider, 11, [cartographic]);
            if (heights[0]) {
                point.height = heights[0].height || 0;
            }
        } catch (error) {
            console.warn('计算地面高度失败:', error);
            point.height = 0;
        }
    }

    // 开始飞行漫游
    startFly(routeId, options = {}) {
        const route = this.getRouteById(routeId);
        if (!route || route.points.length < 2) {
            console.warn('路径不存在或路径点少于2个');
            return false;
        }

        this.stopFly(); // 停止当前飞行
        this.currentRoute = route;

        const flyOptions = {
            duration: options.duration || route.duration,
            height: options.height || route.height,
            clampToGround: route.clampToGround,
            showPath: route.showPath,
            pathColor: route.pathColor,
            ...options
        };

        this.createFlyPath(route, flyOptions);
        this.startAnimation(flyOptions.duration);
        
        this.isFlying = true;
        return true;
    }

    // 创建飞行路径
    createFlyPath(route, options) {
        const start = Cesium.JulianDate.now();
        const stop = Cesium.JulianDate.addSeconds(start, options.duration, new Cesium.JulianDate());
        
        // 设置时钟范围
        this.clock.startTime = start;
        this.clock.stopTime = stop;
        this.clock.currentTime = start;
        this.clock.clockRange = Cesium.ClockRange.CLAMPED;
        this.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK_MULTIPLIER;

        // 创建位置属性
        const positionProperty = new Cesium.SampledPositionProperty();
        
        // 设置插值选项以获得平滑的飞行轨迹
        positionProperty.setInterpolationOptions({
            interpolationDegree: 2,
            interpolationAlgorithm: Cesium.LagrangePolynomialApproximation
        });
        
        const timeStep = options.duration / (route.points.length - 1);

        route.points.forEach((point, index) => {
            const time = Cesium.JulianDate.addSeconds(start, timeStep * index, new Cesium.JulianDate());
            let position = new Cesium.Cartesian3(point.position.x, point.position.y, point.position.z);
            
            // 如果设置了飞行高度
            if (options.height > 0 && !options.clampToGround) {
                const cartographic = Cesium.Cartographic.fromCartesian(position);
                cartographic.height = options.height;
                position = Cesium.Cartographic.toCartesian(cartographic);
            }
            
            positionProperty.addSample(time, position);
        });

        // 创建模型的朝向属性，让模型自动朝向飞行方向
        const orientationProperty = new Cesium.VelocityOrientationProperty(positionProperty);

        // 创建飞行实体
        this.flyEntity = this.viewer.entities.add({
            id: 'roam_fly_entity',
            position: positionProperty,
            orientation: orientationProperty,
            // 使用项目中的战机模型
            model: {
                uri: './src/models/zhanji.glb',
                minimumPixelSize: 64,
                maximumScale: 1000,
                scale: 0.5,
                runAnimations: false
            },
            path: options.showPath ? {
                resolution: 1,
                material: Cesium.Color.fromCssColorString(options.pathColor),
                width: 3,
                leadTime: 0,
                trailTime: options.duration
            } : undefined
        });

        // 设置相机跟随
        this.viewer.trackedEntity = this.flyEntity;
    }

    // 开始动画
    startAnimation(duration) {
        this.clock.shouldAnimate = true;
        this.clock.multiplier = 1;

        // 监听时钟事件
        this.clockListener = this.clock.onTick.addEventListener(() => {
            if (this.isFlying && this.onFlyProgress) {
                const progress = this.calculateFlyProgress();
                this.onFlyProgress(progress);
            }
        });
    }

    // 计算飞行进度
    calculateFlyProgress() {
        const currentTime = this.clock.currentTime;
        const startTime = this.clock.startTime;
        const stopTime = this.clock.stopTime;
        
        const totalSeconds = Cesium.JulianDate.secondsDifference(stopTime, startTime);
        const elapsedSeconds = Cesium.JulianDate.secondsDifference(currentTime, startTime);
        
        const progress = Math.min(Math.max(elapsedSeconds / totalSeconds, 0), 1);
        
        // 计算当前位置信息
        let currentPosition = null;
        let currentHeight = 0;
        let distance = 0;
        
        if (this.flyEntity && this.flyEntity.position) {
            currentPosition = this.flyEntity.position.getValue(currentTime);
            if (currentPosition) {
                const cartographic = Cesium.Cartographic.fromCartesian(currentPosition);
                currentHeight = cartographic.height;
                
                // 计算已飞行距离
                distance = this.calculateFlownDistance(progress);
            }
        }

        return {
            progress: progress,
            elapsedTime: elapsedSeconds,
            totalTime: totalSeconds,
            currentPosition: currentPosition,
            currentHeight: currentHeight,
            distance: distance
        };
    }

    // 计算已飞行距离
    calculateFlownDistance(progress) {
        if (!this.currentRoute || this.currentRoute.points.length < 2) return 0;
        
        const totalDistance = this.calculateRouteDistance(this.currentRoute);
        return totalDistance * progress;
    }

    // 计算路径总距离
    calculateRouteDistance(route) {
        if (route.points.length < 2) return 0;
        
        let totalDistance = 0;
        for (let i = 1; i < route.points.length; i++) {
            const prev = new Cesium.Cartesian3(
                route.points[i-1].position.x,
                route.points[i-1].position.y,
                route.points[i-1].position.z
            );
            const curr = new Cesium.Cartesian3(
                route.points[i].position.x,
                route.points[i].position.y,
                route.points[i].position.z
            );
            
            totalDistance += Cesium.Cartesian3.distance(prev, curr);
        }
        
        return totalDistance;
    }

    // 停止飞行
    stopFly() {
        this.isFlying = false;
        
        if (this.clockListener) {
            this.clockListener();
            this.clockListener = null;
        }
        
        if (this.flyEntity) {
            this.viewer.entities.remove(this.flyEntity);
            this.flyEntity = null;
        }
        
        this.viewer.trackedEntity = undefined;
        this.restoreClockSettings();
        this.currentRoute = null;
    }

    // 恢复时钟设置
    restoreClockSettings() {
        if (this.originalClockSettings) {
            this.clock.startTime = this.originalClockSettings.startTime;
            this.clock.stopTime = this.originalClockSettings.stopTime;
            this.clock.currentTime = this.originalClockSettings.currentTime;
            this.clock.multiplier = this.originalClockSettings.multiplier;
            this.clock.shouldAnimate = this.originalClockSettings.shouldAnimate;
        }
    }

    // 暂停/继续飞行
    pauseFly() {
        if (this.isFlying) {
            this.clock.shouldAnimate = !this.clock.shouldAnimate;
            return !this.clock.shouldAnimate; // 返回是否暂停状态
        }
        return false;
    }

    // 调整飞行速度
    setFlySpeed(multiplier) {
        if (this.isFlying) {
            this.clock.multiplier = multiplier;
        }
    }

    // 生成高度剖面数据
    async generateHeightProfile(routeId) {
        const route = this.getRouteById(routeId);
        if (!route || route.points.length < 2) return null;

        const profileData = {
            routeId: routeId,
            routeName: route.name,
            points: [],
            totalDistance: 0,
            maxHeight: 0,
            minHeight: Number.MAX_VALUE
        };

        let cumulativeDistance = 0;
        
        for (let i = 0; i < route.points.length; i++) {
            const point = route.points[i];
            const position = new Cesium.Cartesian3(point.position.x, point.position.y, point.position.z);
            const cartographic = Cesium.Cartographic.fromCartesian(position);
            
            // 计算距离
            if (i > 0) {
                const prevPosition = new Cesium.Cartesian3(
                    route.points[i-1].position.x,
                    route.points[i-1].position.y,
                    route.points[i-1].position.z
                );
                cumulativeDistance += Cesium.Cartesian3.distance(prevPosition, position);
            }
            
            const height = cartographic.height;
            profileData.points.push({
                index: i,
                distance: cumulativeDistance,
                height: height,
                longitude: Cesium.Math.toDegrees(cartographic.longitude),
                latitude: Cesium.Math.toDegrees(cartographic.latitude)
            });
            
            profileData.maxHeight = Math.max(profileData.maxHeight, height);
            profileData.minHeight = Math.min(profileData.minHeight, height);
        }
        
        profileData.totalDistance = cumulativeDistance;
        return profileData;
    }

    // 删除路径
    deleteRoute(routeId) {
        const index = this.routes.findIndex(r => r.id === routeId);
        if (index > -1) {
            this.routes.splice(index, 1);
            this.saveRoutes();
            return true;
        }
        return false;
    }

    // 获取路径
    getRouteById(routeId) {
        return this.routes.find(r => r.id === routeId);
    }

    // 获取所有路径
    getAllRoutes() {
        return this.routes;
    }

    // 导入路径数据
    importRoute(routeData) {
        if (routeData && routeData.points && routeData.points.length > 0) {
            const route = {
                ...routeData,
                id: this.generateId(),
                createTime: new Date().toISOString()
            };
            this.routes.push(route);
            this.saveRoutes();
            return route;
        }
        return null;
    }

    // 导出路径数据
    exportRoute(routeId) {
        const route = this.getRouteById(routeId);
        if (route) {
            return JSON.stringify(route, null, 2);
        }
        return null;
    }

    // 保存路径到本地存储
    saveRoutes() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.routes));
        } catch (error) {
            console.error('保存漫游路径失败:', error);
        }
    }

    // 从本地存储加载路径
    loadRoutes() {
        try {
            const data = localStorage.getItem(this.storageKey);
            if (data) {
                this.routes = JSON.parse(data) || [];
            }
        } catch (error) {
            console.error('加载漫游路径失败:', error);
            this.routes = [];
        }
    }

    // 生成唯一ID
    generateId() {
        return 'roam_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 清理资源
    destroy() {
        this.stopFly();
        this.routes = [];
        this.currentRoute = null;
    }

    // 事件回调
    onFlyProgress = null; // 飞行进度回调
    onFlyComplete = null; // 飞行完成回调
    onFlyStart = null;    // 飞行开始回调
}

// 导出到全局
window.RoamTool = RoamTool;