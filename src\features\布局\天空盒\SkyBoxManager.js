// 天空盒管理类

const BoxGeometry = Cesium.BoxGeometry;
const Cartesian3 = Cesium.Cartesian3;
const defaultValue = Cesium.defaultValue;
const defined = Cesium.defined;
const destroyObject = Cesium.destroyObject;
const DeveloperError = Cesium.DeveloperError;
const GeometryPipeline = Cesium.GeometryPipeline;
const Matrix3 = Cesium.Matrix3;
const Matrix4 = Cesium.Matrix4;
const Transforms = Cesium.Transforms;
const VertexFormat = Cesium.VertexFormat;
const BufferUsage = Cesium.BufferUsage;
const CubeMap = Cesium.CubeMap;
const DrawCommand = Cesium.DrawCommand;
const loadCubeMap = Cesium.loadCubeMap;
const RenderState = Cesium.RenderState;
const VertexArray = Cesium.VertexArray;
const BlendingState = Cesium.BlendingState;
const SceneMode = Cesium.SceneMode;
const ShaderProgram = Cesium.ShaderProgram;
const ShaderSource = Cesium.ShaderSource;

// 天空盒矩阵
const skyboxMatrix3 = new Matrix3();

// 默认天空盒图片路径 - 使用相对路径避免服务器路径问题
const DEFAULT_SKYBOX_IMAGES = {
    positiveX: './src/features/布局/天空盒/images/Right.jpg',
    negativeX: './src/features/布局/天空盒/images/Left.jpg',
    positiveY: './src/features/布局/天空盒/images/Front.jpg',
    negativeY: './src/features/布局/天空盒/images/Back.jpg',
    positiveZ: './src/features/布局/天空盒/images/Up.jpg',
    negativeZ: './src/features/布局/天空盒/images/Down.jpg'
};

/**
 * 近地天空盒实现
 */
class SkyBoxOnGround {
    constructor(options) {
        if (!Cesium.defined(Cesium.Matrix4.getRotation)) {
            Cesium.Matrix4.getRotation = Cesium.Matrix4.getMatrix3;
        }
        this.sources = options.sources;
        this._sources = undefined;
        this.show = options.show ?? true;

        this._command = new DrawCommand({
            modelMatrix: Matrix4.clone(Matrix4.IDENTITY),
            owner: this,
        });
        this._cubeMap = undefined;
        this._attributeLocations = undefined;
        this._useHdr = undefined;
    }

    update(frameState, useHdr) {
        if (!this.show || frameState.mode !== SceneMode.SCENE3D && frameState.mode !== SceneMode.MORPHING || !frameState.passes.render) {
            return undefined;
        }

        const SkyBoxFS = `precision highp float;
            uniform samplerCube u_cubeMap;
            in vec3 v_texCoord;
            out vec4 fragColor;
            void main() {
                vec4 color = texture(u_cubeMap, normalize(v_texCoord));
                fragColor = vec4(czm_gammaCorrect(color).rgb, czm_morphTime);
            }`;

        const SkyBoxVS = `#version 300 es
            precision highp float;
            in vec3 position;
            out vec3 v_texCoord;
            uniform mat3 u_rotateMatrix;
            void main() {
                vec3 p = czm_viewRotation * u_rotateMatrix * (czm_temeToPseudoFixed * (czm_entireFrustum.y * position));
                gl_Position = czm_projection * vec4(p, 1.0);
                v_texCoord = position;
            }`;

        const context = frameState.context;

        if (this._sources !== this.sources) {
            this._sources = this.sources;
            const sources = this.sources;

            if (!defined(sources.positiveX) || !defined(sources.negativeX) ||
                !defined(sources.positiveY) || !defined(sources.negativeY) ||
                !defined(sources.positiveZ) || !defined(sources.negativeZ)) {
                throw new DeveloperError("Sources must have all six cube map faces");
            }

            const allSourcesString = typeof sources.positiveX === "string";
            if ([sources.negativeX, sources.positiveY, sources.negativeY, 
                 sources.positiveZ, sources.negativeZ].some(src => typeof src === "string" !== allSourcesString)) {
                throw new DeveloperError("All sources must be strings or all must be images");
            }

            if (allSourcesString) {
                loadCubeMap(context, this._sources).then((cubeMap) => {
                    this._cubeMap = this._cubeMap && this._cubeMap.destroy();
                    this._cubeMap = cubeMap;
                });
            } else {
                this._cubeMap = this._cubeMap && this._cubeMap.destroy();
                this._cubeMap = new CubeMap({
                    context: context,
                    source: sources,
                });
            }
        }

        const command = this._command;

        command.modelMatrix = Transforms.eastNorthUpToFixedFrame(frameState.camera._positionWC);

        if (!defined(command.vertexArray)) {
            command.uniformMap = {
                u_cubeMap: () => this._cubeMap,
                u_rotateMatrix: () => Matrix4.getRotation(command.modelMatrix, skyboxMatrix3)
            };

            const geometry = BoxGeometry.createGeometry(
                BoxGeometry.fromDimensions({
                    dimensions: new Cartesian3(2.0, 2.0, 2.0),
                    vertexFormat: VertexFormat.POSITION_ONLY,
                })
            );

            this._attributeLocations = GeometryPipeline.createAttributeLocations(geometry);

            command.vertexArray = VertexArray.fromGeometry({
                context: context,
                geometry: geometry,
                attributeLocations: this._attributeLocations,
                bufferUsage: BufferUsage._DRAW,
            });

            command.renderState = RenderState.fromCache({
                blending: BlendingState.ALPHA_BLEND,
            });
        }

        if (!defined(command.shaderProgram) || this._useHdr !== useHdr) {
            const fs = new ShaderSource({
                defines: [useHdr ? "HDR" : ""],
                sources: [SkyBoxFS],
            });
            command.shaderProgram = ShaderProgram.fromCache({
                context: context,
                vertexShaderSource: SkyBoxVS,
                fragmentShaderSource: fs,
                attributeLocations: this._attributeLocations,
            });
            this._useHdr = useHdr;
        }

        return defined(this._cubeMap) ? command : undefined;
    }

    isDestroyed() {
        return false;
    }

    destroy() {
        const command = this._command;
        command.vertexArray = command.vertexArray && command.vertexArray.destroy();
        command.shaderProgram = command.shaderProgram && command.shaderProgram.destroy();
        this._cubeMap = this._cubeMap && this._cubeMap.destroy();
        return destroyObject(this);
    }
}

/**
 * 天空盒管理类
 */
class SkyBoxManager {
    constructor(viewer) {
        this.viewer = viewer;
        this.sources = {};
    }

    /**
     * 初始化默认天空盒
     * @param {Cesium.Viewer} viewer - Cesium Viewer实例
     * @param {Object} [options] - 配置选项
     * @param {Object} [options.sources] - 天空盒贴图源，默认使用内置图片
     * @param {number} [options.height=225705] - 切换高度
     * @returns {SkyBoxManager} 天空盒管理器实例
     */
    static initDefaultSkyBox(viewer, options = {}) {
        const skyboxManager = new SkyBoxManager(viewer);
        
        const sources = options.sources || DEFAULT_SKYBOX_IMAGES;
        const height = options.height; // 使用setupGroundSkyBox中的默认值
        
        skyboxManager.setupGroundSkyBox({
            sources: sources,
            height: height
        });
        
        return skyboxManager;
    }

    /**
     * 设置自定义天空盒
     * @param {Object} sources - 天空盒贴图源
     */
    customSkyBox(sources) {
        this.viewer.scene.skyBox = new Cesium.SkyBox({
            sources: sources
        });
    }

    /**
     * 设置近地天空盒
     * @param {Object} options - 配置选项
     * @param {Object} options.sources - 天空盒贴图源
     * @param {number} [options.height=225705] - 切换高度
     */
    setupGroundSkyBox(options) {
        const height = options.height ?? 225705;
        const defaultSkyBox = this.viewer.scene.skyBox;
        const groundSkyBox = new SkyBoxOnGround({ sources: options.sources });

        this.viewer.scene.postRender.addEventListener(() => {
            const cameraPosition = this.viewer.camera.position;
            const cameraHeight = Cesium.Cartographic.fromCartesian(cameraPosition).height;

            if (cameraHeight < height) {
                this.viewer.scene.skyBox = groundSkyBox;
                this.viewer.scene.skyAtmosphere.show = false;
            } else {
                this.viewer.scene.skyBox = defaultSkyBox;
                this.viewer.scene.skyAtmosphere.show = true;
            }
        });
    }
}

// 导出类
window.SkyBoxManager = SkyBoxManager; 