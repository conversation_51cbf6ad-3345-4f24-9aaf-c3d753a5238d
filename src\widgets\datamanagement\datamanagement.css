/* 数据管理工具样式 */
.data-management-panel {
    position: fixed;
    top: 80px;
    left: 20px;
    width: 380px;
    max-height: 80vh;
    overflow-y: auto;
    background: rgba(42, 42, 42, 0.9);
    border: 1px solid #555;
    border-radius: 8px;
    color: #fff;
    font-size: 13px;
    z-index: 9999;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.data-management-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid #555;
    border-radius: 8px 8px 0 0;
}

.data-management-panel .panel-header h3 {
    margin: 0;
    font-size: 14px;
    color: #00ffff;
    cursor: pointer;
    user-select: none;
}

.data-management-panel .panel-tabs {
    display: flex;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid #555;
}

.data-management-panel .tab-btn {
    flex: 1;
    padding: 10px 12px;
    background: transparent;
    border: none;
    color: #aaa;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    border-bottom: 3px solid transparent;
}

.data-management-panel .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.data-management-panel .tab-btn.active {
    background: rgba(0, 255, 255, 0.1);
    color: #00ffff;
    border-bottom-color: #00ffff;
}

.data-management-panel .panel-content {
    padding: 16px;
}

/* 通用区域样式 */
.section {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section h4 {
    margin: 0 0 12px 0;
    color: #00ffff;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.section h5 {
    margin: 8px 0 6px 0;
    color: #ccc;
    font-size: 12px;
}

/* 分享容器 */
.share-container {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.quick-share {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.share-url-container {
    background: rgba(255, 255, 255, 0.05);
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #555;
}

.url-input-group {
    display: flex;
    gap: 4px;
}

.url-input-group input {
    flex: 1;
    padding: 6px 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #666;
    border-radius: 3px;
    color: #fff;
    font-size: 11px;
}

.share-options {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-top: 8px;
}

.share-options label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: #ccc;
}

.save-state {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.saved-states {
    max-height: 200px;
    overflow-y: auto;
    margin-top: 8px;
}

.saved-state-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
    margin-bottom: 6px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.saved-state-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: #00ffff;
}

.state-info {
    flex: 1;
    min-width: 0;
}

.state-name {
    font-weight: bold;
    color: #00ffff;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.state-time {
    font-size: 10px;
    color: #888;
}

.state-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
}

.import-export {
    display: flex;
    gap: 8px;
}

/* 打印容器 */
.print-container {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.print-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.form-row {
    display: flex;
    gap: 12px;
}

.form-row .form-group {
    flex: 1;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.form-group label {
    font-size: 11px;
    color: #ccc;
    font-weight: normal;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 6px 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #555;
    border-radius: 3px;
    color: #fff;
    font-size: 11px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00ffff;
    background: rgba(255, 255, 255, 0.15);
}

.form-group input[type="range"] {
    padding: 0;
    height: 20px;
}

.quality-value {
    font-size: 10px;
    color: #aaa;
    text-align: right;
    margin-top: -4px;
}

.decoration-options {
    margin-top: 8px;
}

.checkbox-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
    margin-top: 6px;
}

.checkbox-grid label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: #ccc;
}

.print-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.preview-container {
    margin-top: 8px;
}

.screenshot-preview {
    border: 1px solid #555;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.05);
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.preview-placeholder {
    text-align: center;
    color: #666;
    font-size: 11px;
}

.preview-placeholder i {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

/* 书签容器 */
.bookmark-container {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.create-bookmark {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.bookmark-list {
    max-height: 300px;
    overflow-y: auto;
}

.bookmark-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    margin-bottom: 8px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.bookmark-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: #00ffff;
}

.bookmark-info {
    flex: 1;
    min-width: 0;
}

.bookmark-name {
    font-weight: bold;
    color: #00ffff;
    font-size: 12px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bookmark-detail {
    font-size: 10px;
    color: #aaa;
    margin-bottom: 2px;
}

.bookmark-desc {
    color: #ccc;
    font-style: italic;
}

.bookmark-time {
    font-size: 10px;
    color: #666;
}

.bookmark-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
}

.bookmark-import-export {
    display: flex;
    gap: 8px;
}

/* 通用样式 */
.no-data {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
    font-size: 11px;
}

.btn {
    padding: 6px 12px;
    background: #007acc;
    color: white;
    border: 1px solid #007acc;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    justify-content: center;
}

.btn:hover {
    background: #005999;
    border-color: #005999;
}

.btn.btn-primary {
    background: #00ffff;
    color: #000;
    border-color: #00ffff;
}

.btn.btn-primary:hover {
    background: #00cccc;
    border-color: #00cccc;
}

.btn.btn-secondary {
    background: #6c757d;
    border-color: #6c757d;
}

.btn.btn-secondary:hover {
    background: #545b62;
    border-color: #545b62;
}

.btn.btn-danger {
    background: #dc3545;
    border-color: #dc3545;
}

.btn.btn-danger:hover {
    background: #c82333;
    border-color: #bd2130;
}

.btn.btn-sm {
    padding: 4px 8px;
    font-size: 10px;
    min-width: auto;
}

.btn i {
    font-size: 10px;
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: rgba(42, 42, 42, 0.95);
    border: 1px solid #555;
    border-radius: 8px;
    min-width: 300px;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    color: #fff;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid #555;
    border-radius: 8px 8px 0 0;
}

.modal-header h4 {
    margin: 0;
    color: #00ffff;
    font-size: 14px;
}

.modal-body {
    padding: 16px;
    text-align: center;
}

.modal-body img {
    max-width: 100%;
    height: auto;
}

.modal-body p {
    margin: 12px 0 0 0;
    color: #ccc;
    font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .data-management-panel {
        width: 350px;
        left: 10px;
    }
    
    .social-share {
        grid-template-columns: 1fr;
    }
    
    .print-actions {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .checkbox-grid {
        grid-template-columns: 1fr;
    }
}

/* 滚动条样式 */
.data-management-panel::-webkit-scrollbar,
.saved-states::-webkit-scrollbar,
.bookmark-list::-webkit-scrollbar {
    width: 6px;
}

.data-management-panel::-webkit-scrollbar-track,
.saved-states::-webkit-scrollbar-track,
.bookmark-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.data-management-panel::-webkit-scrollbar-thumb,
.saved-states::-webkit-scrollbar-thumb,
.bookmark-list::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 255, 0.5);
    border-radius: 3px;
}

.data-management-panel::-webkit-scrollbar-thumb:hover,
.saved-states::-webkit-scrollbar-thumb:hover,
.bookmark-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 255, 0.7);
}