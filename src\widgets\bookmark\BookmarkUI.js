/**
 * 书签管理UI界面
 * 提供书签的可视化管理功能
 */
class BookmarkUI {
    constructor(viewer, containerId) {
        this.viewer = viewer;
        this.containerId = containerId;
        this.bookmarkTool = new BookmarkTool(viewer);
        this.panel = null;
        this.isVisible = false;
        this.currentBookmarks = [];
        
        this.init();
    }

    init() {
        this.createPanel();
        this.bindEvents();
        this.refreshBookmarkList();
    }

    createPanel() {
        const panelHtml = `
            <div id="bookmarkPanel" class="tool-panel">
                <div class="tool-panel-header">
                    <div class="tool-panel-title">
                        <i class="fa fa-bookmark"></i>
                        书签管理
                    </div>
                    <button class="tool-panel-close" onclick="bookmarkUI.hide()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="tool-panel-content">
                    <!-- 操作工具栏 -->
                    <div class="bookmark-toolbar">
                        <div class="toolbar-row">
                            <button id="btnSaveBookmark" class="cesium-button cesium-button-primary">
                                <i class="fa fa-bookmark"></i> 保存当前视点
                            </button>
                        </div>
                        <div class="toolbar-row">
                            <div class="search-box">
                                <input type="text" id="bookmarkSearch" placeholder="搜索书签..." class="search-input">
                                <button id="btnSearch" class="search-btn">
                                    <i class="fa fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="toolbar-row">
                            <select id="sortBookmarks" class="sort-select">
                                <option value="time_desc">按时间降序</option>
                                <option value="time_asc">按时间升序</option>
                                <option value="name_asc">按名称升序</option>
                                <option value="name_desc">按名称降序</option>
                            </select>
                            <button id="btnImport" class="cesium-button" title="导入书签">
                                <i class="fa fa-upload"></i>
                            </button>
                            <button id="btnExport" class="cesium-button" title="导出书签">
                                <i class="fa fa-download"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 书签列表 -->
                    <div class="bookmark-list-container">
                        <div class="list-header">
                            <span id="bookmarkCount">书签列表 (0)</span>
                            <button id="btnClearAll" class="clear-all-btn">清空全部</button>
                        </div>
                        <div id="bookmarkList" class="bookmark-list">
                            <!-- 书签项目将显示在这里 -->
                        </div>
                    </div>
                    
                    <!-- 当前位置信息 -->
                    <div class="current-position">
                        <div class="position-header">
                            <span>当前位置</span>
                            <button id="btnRefreshPosition" class="refresh-btn">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                        <div id="currentPositionInfo" class="position-info">
                            <!-- 当前位置信息将显示在这里 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 隐藏的文件输入 -->
            <input type="file" id="bookmarkFileInput" accept=".json" style="display: none;">
        `;

        document.body.insertAdjacentHTML('beforeend', panelHtml);
        this.panel = document.getElementById('bookmarkPanel');
    }

    bindEvents() {
        // 保存书签
        document.getElementById('btnSaveBookmark').addEventListener('click', () => {
            this.showSaveDialog();
        });

        // 搜索
        document.getElementById('bookmarkSearch').addEventListener('input', (e) => {
            this.searchBookmarks(e.target.value);
        });

        document.getElementById('btnSearch').addEventListener('click', () => {
            const keyword = document.getElementById('bookmarkSearch').value;
            this.searchBookmarks(keyword);
        });

        // 排序
        document.getElementById('sortBookmarks').addEventListener('change', (e) => {
            this.sortBookmarks(e.target.value);
        });

        // 导入导出
        document.getElementById('btnImport').addEventListener('click', () => {
            document.getElementById('bookmarkFileInput').click();
        });

        document.getElementById('bookmarkFileInput').addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.importBookmarks(e.target.files[0]);
            }
        });

        document.getElementById('btnExport').addEventListener('click', () => {
            this.bookmarkTool.exportBookmarks();
        });

        // 清空全部
        document.getElementById('btnClearAll').addEventListener('click', () => {
            this.clearAllBookmarks();
        });

        // 刷新位置
        document.getElementById('btnRefreshPosition').addEventListener('click', () => {
            this.updateCurrentPosition();
        });

        // 相机移动事件
        this.viewer.camera.moveEnd.addEventListener(() => {
            this.updateCurrentPosition();
        });
    }

    // 显示保存对话框
    showSaveDialog() {
        const name = prompt('请输入书签名称:', `书签_${new Date().toLocaleString()}`);
        if (name) {
            const description = prompt('请输入书签描述 (可选):', '');
            const bookmark = this.bookmarkTool.saveCurrentView(name, description);
            this.refreshBookmarkList();
            this.showMessage(`书签 "${bookmark.name}" 保存成功！`);
        }
    }

    // 搜索书签
    searchBookmarks(keyword) {
        this.currentBookmarks = this.bookmarkTool.searchBookmarks(keyword);
        this.renderBookmarkList();
    }

    // 排序书签
    sortBookmarks(sortType) {
        switch (sortType) {
            case 'time_desc':
                this.bookmarkTool.sortBookmarksByTime(false);
                break;
            case 'time_asc':
                this.bookmarkTool.sortBookmarksByTime(true);
                break;
            case 'name_asc':
                this.bookmarkTool.sortBookmarksByName(true);
                break;
            case 'name_desc':
                this.bookmarkTool.sortBookmarksByName(false);
                break;
        }
        this.refreshBookmarkList();
    }

    // 导入书签
    async importBookmarks(file) {
        try {
            const count = await this.bookmarkTool.importBookmarks(file);
            this.refreshBookmarkList();
            this.showMessage(`成功导入 ${count} 个书签！`);
        } catch (error) {
            this.showMessage(`导入失败: ${error.message}`, 'error');
        }
    }

    // 清空所有书签
    clearAllBookmarks() {
        if (confirm('确定要清空所有书签吗？此操作不可恢复！')) {
            this.bookmarkTool.clearAllBookmarks();
            this.refreshBookmarkList();
            this.showMessage('所有书签已清空！');
        }
    }

    // 刷新书签列表
    refreshBookmarkList() {
        this.currentBookmarks = this.bookmarkTool.getAllBookmarks();
        this.renderBookmarkList();
        this.updateBookmarkCount();
    }

    // 渲染书签列表
    renderBookmarkList() {
        const listContainer = document.getElementById('bookmarkList');
        listContainer.innerHTML = '';

        if (this.currentBookmarks.length === 0) {
            listContainer.innerHTML = `
                <div class="empty-message">
                    <i class="fa fa-bookmark-o"></i>
                    <p>暂无书签</p>
                    <p>点击"保存当前视点"创建第一个书签</p>
                </div>
            `;
            return;
        }

        this.currentBookmarks.forEach(bookmark => {
            const bookmarkItem = this.createBookmarkItem(bookmark);
            listContainer.appendChild(bookmarkItem);
        });
    }

    // 创建书签项目
    createBookmarkItem(bookmark) {
        const item = document.createElement('div');
        item.className = 'bookmark-item';
        item.innerHTML = `
            <div class="bookmark-header">
                <div class="bookmark-name" title="${bookmark.name}">${bookmark.name}</div>
                <div class="bookmark-actions">
                    <button class="action-btn" onclick="bookmarkUI.flyToBookmark('${bookmark.id}')" title="飞行到此位置">
                        <i class="fa fa-paper-plane"></i>
                    </button>
                    <button class="action-btn" onclick="bookmarkUI.jumpToBookmark('${bookmark.id}')" title="直接跳转">
                        <i class="fa fa-crosshairs"></i>
                    </button>
                    <button class="action-btn" onclick="bookmarkUI.editBookmark('${bookmark.id}')" title="编辑">
                        <i class="fa fa-edit"></i>
                    </button>
                    <button class="action-btn action-btn-danger" onclick="bookmarkUI.deleteBookmark('${bookmark.id}')" title="删除">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="bookmark-info">
                <div class="bookmark-description">${bookmark.description || '无描述'}</div>
                <div class="bookmark-coordinates">
                    经度: ${bookmark.geographic.longitude}° | 纬度: ${bookmark.geographic.latitude}° | 高度: ${bookmark.geographic.height}m
                </div>
                <div class="bookmark-time">创建时间: ${new Date(bookmark.createTime).toLocaleString()}</div>
            </div>
        `;
        return item;
    }

    // 飞行到书签
    flyToBookmark(bookmarkId) {
        this.bookmarkTool.flyToBookmark(bookmarkId);
    }

    // 跳转到书签
    jumpToBookmark(bookmarkId) {
        this.bookmarkTool.jumpToBookmark(bookmarkId);
    }

    // 编辑书签
    editBookmark(bookmarkId) {
        const bookmark = this.bookmarkTool.getBookmarkById(bookmarkId);
        if (bookmark) {
            const newName = prompt('修改书签名称:', bookmark.name);
            if (newName && newName !== bookmark.name) {
                const newDescription = prompt('修改书签描述:', bookmark.description || '');
                this.bookmarkTool.updateBookmark(bookmarkId, {
                    name: newName,
                    description: newDescription
                });
                this.refreshBookmarkList();
                this.showMessage('书签信息已更新！');
            }
        }
    }

    // 删除书签
    deleteBookmark(bookmarkId) {
        const bookmark = this.bookmarkTool.getBookmarkById(bookmarkId);
        if (bookmark && confirm(`确定要删除书签 "${bookmark.name}" 吗？`)) {
            this.bookmarkTool.deleteBookmark(bookmarkId);
            this.refreshBookmarkList();
            this.showMessage('书签已删除！');
        }
    }

    // 更新书签数量
    updateBookmarkCount() {
        const countElement = document.getElementById('bookmarkCount');
        const total = this.bookmarkTool.getAllBookmarks().length;
        const current = this.currentBookmarks.length;
        
        if (current === total) {
            countElement.textContent = `书签列表 (${total})`;
        } else {
            countElement.textContent = `书签列表 (${current}/${total})`;
        }
    }

    // 更新当前位置信息
    updateCurrentPosition() {
        const info = this.bookmarkTool.getCurrentCameraInfo();
        const infoElement = document.getElementById('currentPositionInfo');
        
        infoElement.innerHTML = `
            <div class="position-row">
                <span class="position-label">经度:</span>
                <span class="position-value">${info.longitude}°</span>
            </div>
            <div class="position-row">
                <span class="position-label">纬度:</span>
                <span class="position-value">${info.latitude}°</span>
            </div>
            <div class="position-row">
                <span class="position-label">高度:</span>
                <span class="position-value">${info.height}m</span>
            </div>
            <div class="position-row">
                <span class="position-label">方向:</span>
                <span class="position-value">${info.heading}°</span>
            </div>
            <div class="position-row">
                <span class="position-label">俯仰:</span>
                <span class="position-value">${info.pitch}°</span>
            </div>
        `;
    }

    // 显示消息
    showMessage(message, type = 'info') {
        console.log(`[书签管理] ${message}`);
        // 可以在这里实现更好的消息提示UI
    }

    // 显示面板
    show() {
        if (this.panel) {
            this.panel.classList.add('show');
            this.isVisible = true;
            this.refreshBookmarkList();
            this.updateCurrentPosition();
        }
    }

    // 隐藏面板
    hide() {
        if (this.panel) {
            this.panel.classList.remove('show');
            this.isVisible = false;
        }
    }

    // 切换显示状态
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    // 销毁
    destroy() {
        if (this.bookmarkTool) {
            this.bookmarkTool.destroy();
        }
        if (this.panel) {
            this.panel.remove();
        }
    }
}

// 导出
window.BookmarkUI = BookmarkUI;
