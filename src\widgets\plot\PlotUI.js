/**
 * 标绘工具UI界面
 * 提供完整的标绘功能用户界面
 */
class PlotUI {
    constructor(viewer, containerId) {
        this.viewer = viewer;
        this.containerId = containerId;
        this.plotTool = new PlotTool(viewer);
        this.panel = null;
        this.isVisible = false;
        this.currentTool = null;
        
        this.init();
    }

    init() {
        this.createPanel();
        this.bindEvents();
    }

    createPanel() {
        const panelHtml = `
            <div id="plotPanel" class="tool-panel">
                <div class="tool-panel-header">
                    <div class="tool-panel-title">
                        <i class="fa fa-pencil"></i>
                        标绘工具
                    </div>
                    <button class="tool-panel-close" onclick="plotUI.hide()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="tool-panel-content">
                    <div class="plot-toolbar">
                        <div class="plot-group">
                            <label>基础图形</label>
                            <div class="plot-buttons">
                                <button id="btnPlotPoint" class="cesium-button" title="点标记">
                                    <i class="fa fa-map-marker"></i> 点
                                </button>
                                <button id="btnPlotPolyline" class="cesium-button" title="线">
                                    <i class="fa fa-minus"></i> 线
                                </button>
                                <button id="btnPlotPolygon" class="cesium-button" title="多边形">
                                    <i class="fa fa-square-o"></i> 面
                                </button>
                            </div>
                        </div>
                        
                        <div class="plot-group">
                            <label>几何图形</label>
                            <div class="plot-buttons">
                                <button id="btnPlotCircle" class="cesium-button" title="圆">
                                    <i class="fa fa-circle-o"></i> 圆
                                </button>
                                <button id="btnPlotRectangle" class="cesium-button" title="矩形">
                                    <i class="fa fa-square-o"></i> 矩形
                                </button>
                                <button id="btnPlotEllipse" class="cesium-button" title="椭圆">
                                    <i class="fa fa-ellipsis-h"></i> 椭圆
                                </button>
                            </div>
                        </div>
                        
                        <div class="plot-group">
                            <label>文字标注</label>
                            <div class="plot-buttons">
                                <button id="btnPlotLabel" class="cesium-button" title="文字标注">
                                    <i class="fa fa-font"></i> 文字
                                </button>
                                <button id="btnPlotBillboard" class="cesium-button" title="图标标注">
                                    <i class="fa fa-image"></i> 图标
                                </button>
                            </div>
                        </div>
                        
                        <div class="plot-group">
                            <label>高级功能</label>
                            <div class="plot-buttons">
                                <button id="btnPlotModel" class="cesium-button" title="3D模型">
                                    <i class="fa fa-cube"></i> 模型
                                </button>
                                <button id="btnPlotWall" class="cesium-button" title="墙体">
                                    <i class="fa fa-building"></i> 墙体
                                </button>
                            </div>
                        </div>
                        
                        <div class="plot-group">
                            <label>操作</label>
                            <div class="plot-buttons">
                                <button id="btnPlotEdit" class="cesium-button" title="编辑">
                                    <i class="fa fa-edit"></i> 编辑
                                </button>
                                <button id="btnPlotClear" class="cesium-button cesium-button-danger" title="清除">
                                    <i class="fa fa-trash"></i> 清除
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 样式设置面板 -->
                    <div class="plot-style-panel">
                        <div class="style-header">
                            <span>样式设置</span>
                            <button id="btnToggleStyle" class="toggle-btn">▼</button>
                        </div>
                        <div id="styleContent" class="style-content" style="display: none;">
                            <div class="style-row">
                                <label>颜色:</label>
                                <input type="color" id="plotColor" value="#ffff00">
                            </div>
                            <div class="style-row">
                                <label>透明度:</label>
                                <input type="range" id="plotAlpha" min="0" max="1" step="0.1" value="0.7">
                                <span id="alphaValue">0.7</span>
                            </div>
                            <div class="style-row">
                                <label>线宽:</label>
                                <input type="range" id="plotWidth" min="1" max="10" step="1" value="3">
                                <span id="widthValue">3</span>
                            </div>
                            <div class="style-row">
                                <label>大小:</label>
                                <input type="range" id="plotSize" min="5" max="50" step="5" value="15">
                                <span id="sizeValue">15</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 标绘列表 -->
                    <div class="plot-list-panel">
                        <div class="list-header">
                            <span>标绘列表</span>
                            <button id="btnClearList" class="clear-list-btn">清空</button>
                        </div>
                        <div id="plotList" class="plot-list">
                            <!-- 标绘项目将显示在这里 -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', panelHtml);
        this.panel = document.getElementById('plotPanel');
    }

    bindEvents() {
        // 基础图形
        document.getElementById('btnPlotPoint').addEventListener('click', () => this.startPlotPoint());
        document.getElementById('btnPlotPolyline').addEventListener('click', () => this.startPlotPolyline());
        document.getElementById('btnPlotPolygon').addEventListener('click', () => this.startPlotPolygon());
        
        // 几何图形
        document.getElementById('btnPlotCircle').addEventListener('click', () => this.startPlotCircle());
        document.getElementById('btnPlotRectangle').addEventListener('click', () => this.startPlotRectangle());
        document.getElementById('btnPlotEllipse').addEventListener('click', () => this.startPlotEllipse());
        
        // 文字标注
        document.getElementById('btnPlotLabel').addEventListener('click', () => this.startPlotLabel());
        document.getElementById('btnPlotBillboard').addEventListener('click', () => this.startPlotBillboard());
        
        // 高级功能
        document.getElementById('btnPlotModel').addEventListener('click', () => this.startPlotModel());
        document.getElementById('btnPlotWall').addEventListener('click', () => this.startPlotWall());
        
        // 操作按钮
        document.getElementById('btnPlotEdit').addEventListener('click', () => this.startEdit());
        document.getElementById('btnPlotClear').addEventListener('click', () => this.clearAll());
        
        // 样式控制
        document.getElementById('btnToggleStyle').addEventListener('click', () => this.toggleStylePanel());
        document.getElementById('plotAlpha').addEventListener('input', (e) => {
            document.getElementById('alphaValue').textContent = e.target.value;
        });
        document.getElementById('plotWidth').addEventListener('input', (e) => {
            document.getElementById('widthValue').textContent = e.target.value;
        });
        document.getElementById('plotSize').addEventListener('input', (e) => {
            document.getElementById('sizeValue').textContent = e.target.value;
        });
        
        // 列表操作
        document.getElementById('btnClearList').addEventListener('click', () => this.clearList());
    }

    // 获取当前样式设置
    getCurrentStyle() {
        return {
            color: document.getElementById('plotColor').value,
            alpha: parseFloat(document.getElementById('plotAlpha').value),
            width: parseInt(document.getElementById('plotWidth').value),
            size: parseInt(document.getElementById('plotSize').value)
        };
    }

    // 开始绘制点
    startPlotPoint() {
        this.setActiveTool('btnPlotPoint');
        const style = this.getCurrentStyle();
        
        this.plotTool.drawPoint({
            color: style.color,
            pixelSize: style.size,
            callback: (entity) => {
                this.addToList('点标记', entity);
                this.clearActiveTool();
            }
        });
    }

    // 开始绘制线
    startPlotPolyline() {
        this.setActiveTool('btnPlotPolyline');
        const style = this.getCurrentStyle();
        
        this.plotTool.drawPolyline({
            color: style.color,
            width: style.width,
            callback: (entity) => {
                this.addToList('线', entity);
                this.clearActiveTool();
            }
        });
    }

    // 开始绘制多边形
    startPlotPolygon() {
        this.setActiveTool('btnPlotPolygon');
        const style = this.getCurrentStyle();
        
        this.plotTool.drawPolygon({
            color: style.color,
            alpha: style.alpha,
            callback: (entity) => {
                this.addToList('多边形', entity);
                this.clearActiveTool();
            }
        });
    }

    // 开始绘制圆
    startPlotCircle() {
        this.setActiveTool('btnPlotCircle');
        const style = this.getCurrentStyle();
        
        this.plotTool.drawCircle({
            color: style.color,
            alpha: style.alpha,
            callback: (entity) => {
                this.addToList('圆', entity);
                this.clearActiveTool();
            }
        });
    }

    // 开始绘制矩形
    startPlotRectangle() {
        this.setActiveTool('btnPlotRectangle');
        const style = this.getCurrentStyle();
        
        this.plotTool.drawRectangle({
            color: style.color,
            alpha: style.alpha,
            callback: (entity) => {
                this.addToList('矩形', entity);
                this.clearActiveTool();
            }
        });
    }

    // 开始绘制椭圆
    startPlotEllipse() {
        this.showTip('椭圆绘制功能开发中...');
        setTimeout(() => this.hideTip(), 2000);
    }

    // 开始文字标注
    startPlotLabel() {
        this.setActiveTool('btnPlotLabel');
        
        const text = prompt('请输入标注文字:', '标注文字');
        if (text) {
            const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
            handler.setInputAction((event) => {
                const position = this.viewer.camera.pickEllipsoid(event.position, this.viewer.scene.globe.ellipsoid);
                if (position) {
                    const entity = this.plotTool.addLabel(position, text, {
                        fillColor: this.getCurrentStyle().color
                    });
                    this.addToList('文字标注', entity);
                    handler.destroy();
                    this.clearActiveTool();
                }
            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        } else {
            this.clearActiveTool();
        }
    }

    // 开始图标标注
    startPlotBillboard() {
        this.showTip('图标标注功能开发中...');
        setTimeout(() => this.hideTip(), 2000);
    }

    // 开始3D模型
    startPlotModel() {
        this.showTip('3D模型功能开发中...');
        setTimeout(() => this.hideTip(), 2000);
    }

    // 开始墙体
    startPlotWall() {
        this.showTip('墙体功能开发中...');
        setTimeout(() => this.hideTip(), 2000);
    }

    // 开始编辑
    startEdit() {
        this.showTip('编辑功能开发中...');
        setTimeout(() => this.hideTip(), 2000);
    }

    // 设置活动工具
    setActiveTool(toolId) {
        this.clearActiveTool();
        this.currentTool = toolId;
        const button = document.getElementById(toolId);
        if (button) {
            button.classList.add('active');
        }
    }

    // 清除活动工具
    clearActiveTool() {
        if (this.currentTool) {
            const button = document.getElementById(this.currentTool);
            if (button) {
                button.classList.remove('active');
            }
            this.currentTool = null;
        }
    }

    // 切换样式面板
    toggleStylePanel() {
        const content = document.getElementById('styleContent');
        const btn = document.getElementById('btnToggleStyle');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
            btn.textContent = '▲';
        } else {
            content.style.display = 'none';
            btn.textContent = '▼';
        }
    }

    // 添加到列表
    addToList(type, entity) {
        const listContainer = document.getElementById('plotList');
        const listItem = document.createElement('div');
        listItem.className = 'plot-list-item';
        
        const time = new Date().toLocaleTimeString();
        listItem.innerHTML = `
            <div class="list-item-header">
                <span class="item-type">${type}</span>
                <span class="item-time">${time}</span>
            </div>
            <div class="list-item-actions">
                <button class="item-btn" onclick="plotUI.locateEntity('${entity.id}')">定位</button>
                <button class="item-btn item-btn-danger" onclick="plotUI.removeEntity('${entity.id}')">删除</button>
            </div>
        `;
        
        listContainer.appendChild(listItem);
        listContainer.scrollTop = listContainer.scrollHeight;
        
        // 存储实体引用
        listItem.entityId = entity.id;
    }

    // 定位到实体
    locateEntity(entityId) {
        const entity = this.viewer.entities.getById(entityId);
        if (entity) {
            this.viewer.flyTo(entity);
        }
    }

    // 删除实体
    removeEntity(entityId) {
        const entity = this.viewer.entities.getById(entityId);
        if (entity) {
            this.plotTool.removeEntity(entity);
            
            // 从列表中移除
            const listItems = document.querySelectorAll('.plot-list-item');
            listItems.forEach(item => {
                if (item.entityId === entityId) {
                    item.remove();
                }
            });
        }
    }

    // 显示面板
    show() {
        if (this.panel) {
            this.panel.classList.add('show');
            this.isVisible = true;
        }
    }

    // 隐藏面板
    hide() {
        if (this.panel) {
            this.panel.classList.remove('show');
            this.isVisible = false;
            this.plotTool.deactivate();
            this.clearActiveTool();
        }
    }

    // 切换显示状态
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    // 清除所有标绘
    clearAll() {
        this.plotTool.clearAll();
        this.clearList();
        this.clearActiveTool();
    }

    // 清除列表
    clearList() {
        const listContainer = document.getElementById('plotList');
        if (listContainer) {
            listContainer.innerHTML = '';
        }
    }

    // 显示提示
    showTip(message) {
        console.log('标绘提示:', message);
    }

    // 隐藏提示
    hideTip() {
        // 隐藏提示信息
    }

    // 销毁
    destroy() {
        if (this.plotTool) {
            this.plotTool.destroy();
        }
        if (this.panel) {
            this.panel.remove();
        }
    }
}

// 导出
window.PlotUI = PlotUI;
