/**
 * 打印工具 - 基于原生Cesium实现
 * 提供地图截图、打印和导出功能
 */
class PrintTool {
    constructor(viewer) {
        this.viewer = viewer;
        this.printSettings = {
            format: 'PNG',
            quality: 1.0,
            width: null, // 默认使用画布尺寸
            height: null,
            includeUI: false,
            title: '',
            subtitle: '',
            showScale: true,
            showNorthArrow: true,
            showCoordinates: true,
            showTimestamp: true
        };
    }

    // 截图功能
    captureScreen(options = {}) {
        return new Promise((resolve, reject) => {
            const settings = { ...this.printSettings, ...options };
            
            try {
                // 渲染场景
                this.viewer.render();
                
                setTimeout(() => {
                    try {
                        // 获取画布内容
                        const canvas = this.viewer.canvas;
                        let captureCanvas = canvas;
                        
                        // 如果需要调整尺寸
                        if (settings.width && settings.height && 
                            (settings.width !== canvas.width || settings.height !== canvas.height)) {
                            captureCanvas = this.resizeCanvas(canvas, settings.width, settings.height);
                        }
                        
                        // 添加装饰元素
                        if (settings.showScale || settings.showNorthArrow || 
                            settings.showCoordinates || settings.showTimestamp ||
                            settings.title || settings.subtitle) {
                            captureCanvas = this.addDecorations(captureCanvas, settings);
                        }
                        
                        // 转换为指定格式
                        const result = this.canvasToFormat(captureCanvas, settings);
                        resolve(result);
                        
                    } catch (error) {
                        reject(error);
                    }
                }, 100); // 等待渲染完成
                
            } catch (error) {
                reject(error);
            }
        });
    }

    // 调整画布尺寸
    resizeCanvas(sourceCanvas, targetWidth, targetHeight) {
        const resizedCanvas = document.createElement('canvas');
        resizedCanvas.width = targetWidth;
        resizedCanvas.height = targetHeight;
        
        const ctx = resizedCanvas.getContext('2d');
        ctx.drawImage(sourceCanvas, 0, 0, targetWidth, targetHeight);
        
        return resizedCanvas;
    }

    // 添加装饰元素
    addDecorations(canvas, settings) {
        const decoratedCanvas = document.createElement('canvas');
        const margin = 60; // 边距
        decoratedCanvas.width = canvas.width + margin * 2;
        decoratedCanvas.height = canvas.height + margin * 2 + (settings.title ? 40 : 0) + (settings.subtitle ? 25 : 0);
        
        const ctx = decoratedCanvas.getContext('2d');
        
        // 填充白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, decoratedCanvas.width, decoratedCanvas.height);
        
        let yOffset = 0;
        
        // 添加标题
        if (settings.title) {
            ctx.fillStyle = '#000000';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(settings.title, decoratedCanvas.width / 2, 25);
            yOffset += 40;
        }
        
        // 添加副标题
        if (settings.subtitle) {
            ctx.fillStyle = '#666666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(settings.subtitle, decoratedCanvas.width / 2, yOffset + 20);
            yOffset += 25;
        }
        
        // 绘制主要地图内容
        ctx.drawImage(canvas, margin, yOffset + margin);
        
        // 添加比例尺
        if (settings.showScale) {
            this.drawScale(ctx, margin + 20, yOffset + margin + canvas.height - 60);
        }
        
        // 添加指北针
        if (settings.showNorthArrow) {
            this.drawNorthArrow(ctx, margin + canvas.width - 80, yOffset + margin + 60);
        }
        
        // 添加坐标信息
        if (settings.showCoordinates) {
            this.drawCoordinates(ctx, margin + 20, yOffset + margin + 20);
        }
        
        // 添加时间戳
        if (settings.showTimestamp) {
            const timestamp = new Date().toLocaleString();
            ctx.fillStyle = '#666666';
            ctx.font = '12px Arial';
            ctx.textAlign = 'right';
            ctx.fillText(timestamp, decoratedCanvas.width - margin, decoratedCanvas.height - 20);
        }
        
        return decoratedCanvas;
    }

    // 绘制比例尺
    drawScale(ctx, x, y) {
        const camera = this.viewer.camera;
        const canvas = this.viewer.canvas;
        const width = canvas.clientWidth;
        const height = canvas.clientHeight;
        
        // 计算比例尺
        const leftPoint = new Cesium.Cartesian2(width / 2 - 100, height - 50);
        const rightPoint = new Cesium.Cartesian2(width / 2 + 100, height - 50);
        
        const leftRay = camera.getPickRay(leftPoint);
        const rightRay = camera.getPickRay(rightPoint);
        
        const leftCartesian = this.viewer.scene.globe.pick(leftRay, this.viewer.scene);
        const rightCartesian = this.viewer.scene.globe.pick(rightRay, this.viewer.scene);
        
        if (leftCartesian && rightCartesian) {
            const distance = Cesium.Cartesian3.distance(leftCartesian, rightCartesian);
            
            // 绘制比例尺
            const scaleWidth = 100;
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x + scaleWidth, y);
            ctx.moveTo(x, y - 5);
            ctx.lineTo(x, y + 5);
            ctx.moveTo(x + scaleWidth, y - 5);
            ctx.lineTo(x + scaleWidth, y + 5);
            ctx.stroke();
            
            // 标注距离
            let scaleText = '';
            if (distance >= 1000) {
                scaleText = `${(distance / 1000).toFixed(1)} km`;
            } else {
                scaleText = `${distance.toFixed(0)} m`;
            }
            
            ctx.fillStyle = '#000000';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(scaleText, x + scaleWidth / 2, y + 20);
        }
    }

    // 绘制指北针
    drawNorthArrow(ctx, x, y) {
        const size = 40;
        const camera = this.viewer.camera;
        
        // 计算相机朝向
        const heading = camera.heading;
        
        ctx.save();
        ctx.translate(x, y);
        ctx.rotate(-heading); // 逆时针旋转以指向北方
        
        // 绘制指北针
        ctx.strokeStyle = '#000000';
        ctx.fillStyle = '#ff0000';
        ctx.lineWidth = 2;
        
        // 箭头
        ctx.beginPath();
        ctx.moveTo(0, -size/2);
        ctx.lineTo(-8, -size/4);
        ctx.lineTo(0, -size/2 + 5);
        ctx.lineTo(8, -size/4);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // 箭尾
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.moveTo(0, size/2);
        ctx.lineTo(-6, size/4);
        ctx.lineTo(0, size/2 - 5);
        ctx.lineTo(6, size/4);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // 中心圆
        ctx.fillStyle = '#000000';
        ctx.beginPath();
        ctx.arc(0, 0, 3, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.restore();
        
        // 标注 "N"
        ctx.fillStyle = '#000000';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('N', x, y - size/2 - 10);
    }

    // 绘制坐标信息
    drawCoordinates(ctx, x, y) {
        const camera = this.viewer.camera;
        const position = camera.positionCartographic;
        
        const longitude = Cesium.Math.toDegrees(position.longitude);
        const latitude = Cesium.Math.toDegrees(position.latitude);
        const height = position.height;
        
        ctx.fillStyle = '#000000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        
        const coordText = `中心点: ${longitude.toFixed(6)}°, ${latitude.toFixed(6)}°, ${height.toFixed(0)}m`;
        
        // 绘制背景
        const textMetrics = ctx.measureText(coordText);
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.fillRect(x - 5, y - 15, textMetrics.width + 10, 20);
        
        // 绘制文字
        ctx.fillStyle = '#000000';
        ctx.fillText(coordText, x, y);
    }

    // 转换画布为指定格式
    canvasToFormat(canvas, settings) {
        const format = settings.format.toLowerCase();
        const quality = settings.quality || 1.0;
        
        switch (format) {
            case 'png':
                return canvas.toDataURL('image/png');
            case 'jpeg':
            case 'jpg':
                return canvas.toDataURL('image/jpeg', quality);
            case 'webp':
                return canvas.toDataURL('image/webp', quality);
            case 'canvas':
                return canvas;
            default:
                return canvas.toDataURL('image/png');
        }
    }

    // 下载截图
    async downloadScreenshot(filename = null, options = {}) {
        try {
            const dataUrl = await this.captureScreen(options);
            
            if (!filename) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const format = (options.format || 'PNG').toLowerCase();
                filename = `cesium_screenshot_${timestamp}.${format}`;
            }
            
            // 创建下载链接
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataUrl;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            return true;
        } catch (error) {
            console.error('下载截图失败:', error);
            return false;
        }
    }

    // 打印地图
    async printMap(options = {}) {
        try {
            const canvas = await this.captureScreen({
                ...options,
                format: 'canvas'
            });
            
            // 创建打印窗口
            const printWindow = window.open('', '_blank');
            const img = new Image();
            
            img.onload = () => {
                printWindow.document.write(`
                    <html>
                    <head>
                        <title>地图打印</title>
                        <style>
                            body { margin: 0; padding: 20px; text-align: center; }
                            img { max-width: 100%; height: auto; }
                            @media print {
                                body { margin: 0; padding: 0; }
                                img { width: 100%; height: auto; }
                            }
                        </style>
                    </head>
                    <body>
                        <img src="${img.src}" alt="地图截图" />
                    </body>
                    </html>
                `);
                printWindow.document.close();
                
                // 延迟打印以确保图片加载完成
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            };
            
            img.src = canvas.toDataURL('image/png');
            return true;
            
        } catch (error) {
            console.error('打印地图失败:', error);
            return false;
        }
    }

    // 生成PDF（需要jsPDF库）
    async generatePDF(options = {}) {
        if (typeof jsPDF === 'undefined') {
            console.warn('jsPDF库未加载，无法生成PDF');
            return false;
        }
        
        try {
            const canvas = await this.captureScreen({
                ...options,
                format: 'canvas'
            });
            
            const pdf = new jsPDF({
                orientation: canvas.width > canvas.height ? 'landscape' : 'portrait',
                unit: 'mm',
                format: 'a4'
            });
            
            // 计算缩放比例以适应A4纸张
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();
            const canvasRatio = canvas.width / canvas.height;
            const pdfRatio = pdfWidth / pdfHeight;
            
            let imgWidth, imgHeight;
            if (canvasRatio > pdfRatio) {
                imgWidth = pdfWidth - 20; // 留边距
                imgHeight = imgWidth / canvasRatio;
            } else {
                imgHeight = pdfHeight - 20; // 留边距
                imgWidth = imgHeight * canvasRatio;
            }
            
            const x = (pdfWidth - imgWidth) / 2;
            const y = (pdfHeight - imgHeight) / 2;
            
            // 添加图片到PDF
            const imgData = canvas.toDataURL('image/jpeg', 0.95);
            pdf.addImage(imgData, 'JPEG', x, y, imgWidth, imgHeight);
            
            // 下载PDF
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = options.filename || `cesium_map_${timestamp}.pdf`;
            pdf.save(filename);
            
            return true;
        } catch (error) {
            console.error('生成PDF失败:', error);
            return false;
        }
    }

    // 批量截图（用于动画序列）
    async captureSequence(frames, options = {}) {
        const results = [];
        const interval = options.interval || 1000; // 间隔时间
        
        for (let i = 0; i < frames.length; i++) {
            const frame = frames[i];
            
            // 设置相机位置
            if (frame.camera) {
                this.viewer.camera.setView({
                    destination: Cesium.Cartesian3.fromDegrees(
                        frame.camera.longitude,
                        frame.camera.latitude,
                        frame.camera.height
                    ),
                    orientation: frame.camera.orientation || {}
                });
                
                // 等待场景稳定
                await this.waitForRender();
            }
            
            // 截图
            try {
                const screenshot = await this.captureScreen({
                    ...options,
                    title: frame.title || `帧 ${i + 1}`,
                    subtitle: frame.subtitle || ''
                });
                
                results.push({
                    index: i,
                    data: screenshot,
                    frame: frame
                });
                
            } catch (error) {
                console.error(`截图第${i + 1}帧失败:`, error);
            }
            
            // 间隔等待
            if (i < frames.length - 1) {
                await this.delay(interval);
            }
        }
        
        return results;
    }

    // 等待渲染完成
    waitForRender() {
        return new Promise(resolve => {
            const checkRender = () => {
                this.viewer.render();
                setTimeout(resolve, 200); // 等待渲染稳定
            };
            requestAnimationFrame(checkRender);
        });
    }

    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 设置打印配置
    setSettings(newSettings) {
        this.printSettings = { ...this.printSettings, ...newSettings };
    }

    // 获取当前配置
    getSettings() {
        return { ...this.printSettings };
    }

    // 获取支持的格式
    getSupportedFormats() {
        return ['PNG', 'JPEG', 'WebP'];
    }

    // 获取预设尺寸
    getPresetSizes() {
        return {
            'A4横向': { width: 1123, height: 794 },
            'A4纵向': { width: 794, height: 1123 },
            'A3横向': { width: 1587, height: 1123 },
            'A3纵向': { width: 1123, height: 1587 },
            '1920x1080': { width: 1920, height: 1080 },
            '1280x720': { width: 1280, height: 720 },
            '800x600': { width: 800, height: 600 }
        };
    }

    // 获取当前地图信息
    getMapInfo() {
        const camera = this.viewer.camera;
        const position = camera.positionCartographic;
        
        return {
            center: {
                longitude: Cesium.Math.toDegrees(position.longitude),
                latitude: Cesium.Math.toDegrees(position.latitude),
                height: position.height
            },
            orientation: {
                heading: camera.heading,
                pitch: camera.pitch,
                roll: camera.roll
            },
            timestamp: new Date().toISOString()
        };
    }
}

// 导出到全局
window.PrintTool = PrintTool;