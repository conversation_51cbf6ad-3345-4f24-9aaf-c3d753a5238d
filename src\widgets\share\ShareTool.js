/**
 * 分享工具 - 基于原生Cesium实现
 * 提供视点分享、数据分享和URL生成功能
 */
class ShareTool {
    constructor(viewer) {
        this.viewer = viewer;
        this.baseUrl = window.location.origin + window.location.pathname;
        this.shareKey = 'cesium_shared_state';
    }

    // 生成当前状态分享链接
    generateShareUrl(options = {}) {
        const state = this.getCurrentState(options);
        const stateString = this.encodeState(state);
        
        // 生成分享URL
        const url = new URL(this.baseUrl);
        url.searchParams.set('share', stateString);
        
        if (options.title) {
            url.searchParams.set('title', encodeURIComponent(options.title));
        }
        
        return url.toString();
    }

    // 获取当前状态
    getCurrentState(options = {}) {
        const camera = this.viewer.camera;
        const position = camera.position.clone();
        const orientation = {
            heading: camera.heading,
            pitch: camera.pitch,
            roll: camera.roll
        };

        // 转换为地理坐标
        const cartographic = Cesium.Cartographic.fromCartesian(position);
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        const height = cartographic.height;

        const state = {
            camera: {
                position: {
                    longitude: longitude,
                    latitude: latitude,
                    height: height
                },
                orientation: {
                    heading: orientation.heading,
                    pitch: orientation.pitch,
                    roll: orientation.roll
                }
            },
            timestamp: Date.now(),
            version: '1.0'
        };

        // 可选：包含地形信息
        if (options.includeTerrain) {
            state.terrain = {
                enabled: this.viewer.terrainProvider !== Cesium.EllipsoidTerrainProvider.prototype
            };
        }

        // 可选：包含图层信息
        if (options.includeLayers) {
            state.layers = this.getLayersState();
        }

        // 可选：包含实体信息
        if (options.includeEntities) {
            state.entities = this.getEntitiesState();
        }

        return state;
    }

    // 获取图层状态
    getLayersState() {
        const layers = [];
        
        // 获取影像图层状态
        for (let i = 0; i < this.viewer.imageryLayers.length; i++) {
            const layer = this.viewer.imageryLayers.get(i);
            layers.push({
                type: 'imagery',
                alpha: layer.alpha,
                brightness: layer.brightness,
                contrast: layer.contrast,
                hue: layer.hue,
                saturation: layer.saturation,
                gamma: layer.gamma,
                show: layer.show
            });
        }

        return layers;
    }

    // 获取实体状态（简化版本）
    getEntitiesState() {
        const entities = [];
        
        this.viewer.entities.values.forEach(entity => {
            // 只保存基本信息，避免循环引用
            const entityData = {
                id: entity.id,
                name: entity.name,
                show: entity.show
            };
            
            // 保存位置信息
            if (entity.position) {
                const position = entity.position.getValue(this.viewer.clock.currentTime);
                if (position) {
                    const cartographic = Cesium.Cartographic.fromCartesian(position);
                    entityData.position = {
                        longitude: Cesium.Math.toDegrees(cartographic.longitude),
                        latitude: Cesium.Math.toDegrees(cartographic.latitude),
                        height: cartographic.height
                    };
                }
            }
            
            entities.push(entityData);
        });
        
        return entities;
    }

    // 编码状态为字符串
    encodeState(state) {
        try {
            const jsonString = JSON.stringify(state);
            // 使用Base64编码压缩URL长度
            return btoa(encodeURIComponent(jsonString));
        } catch (error) {
            console.error('编码状态失败:', error);
            return null;
        }
    }

    // 解码状态字符串
    decodeState(stateString) {
        try {
            const decodedString = decodeURIComponent(atob(stateString));
            return JSON.parse(decodedString);
        } catch (error) {
            console.error('解码状态失败:', error);
            return null;
        }
    }

    // 从URL参数恢复状态
    restoreFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        const shareParam = urlParams.get('share');
        
        if (shareParam) {
            const state = this.decodeState(shareParam);
            if (state) {
                this.applyState(state);
                return true;
            }
        }
        
        return false;
    }

    // 应用状态到场景
    applyState(state) {
        if (!state || !state.camera) return false;

        try {
            // 恢复相机状态
            const camera = state.camera;
            const position = Cesium.Cartesian3.fromDegrees(
                camera.position.longitude,
                camera.position.latitude,
                camera.position.height
            );

            this.viewer.camera.setView({
                position: position,
                orientation: {
                    heading: camera.orientation.heading,
                    pitch: camera.orientation.pitch,
                    roll: camera.orientation.roll
                }
            });

            // 恢复图层状态
            if (state.layers) {
                this.applyLayersState(state.layers);
            }

            // 恢复实体状态
            if (state.entities) {
                this.applyEntitiesState(state.entities);
            }

            return true;
        } catch (error) {
            console.error('应用状态失败:', error);
            return false;
        }
    }

    // 应用图层状态
    applyLayersState(layersState) {
        layersState.forEach((layerState, index) => {
            if (index < this.viewer.imageryLayers.length) {
                const layer = this.viewer.imageryLayers.get(index);
                layer.alpha = layerState.alpha || 1.0;
                layer.brightness = layerState.brightness || 1.0;
                layer.contrast = layerState.contrast || 1.0;
                layer.hue = layerState.hue || 0.0;
                layer.saturation = layerState.saturation || 1.0;
                layer.gamma = layerState.gamma || 1.0;
                layer.show = layerState.show !== false;
            }
        });
    }

    // 应用实体状态
    applyEntitiesState(entitiesState) {
        // 简化实现，只恢复基本的显示状态
        entitiesState.forEach(entityData => {
            const entity = this.viewer.entities.getById(entityData.id);
            if (entity) {
                entity.show = entityData.show !== false;
            }
        });
    }

    // 保存状态到本地存储
    saveStateLocally(name, state = null) {
        if (!state) {
            state = this.getCurrentState({ 
                includeLayers: true, 
                includeEntities: true 
            });
        }

        const savedStates = this.getSavedStates();
        const stateData = {
            name: name,
            state: state,
            createTime: new Date().toISOString()
        };

        savedStates.push(stateData);
        
        try {
            localStorage.setItem(this.shareKey, JSON.stringify(savedStates));
            return true;
        } catch (error) {
            console.error('保存状态失败:', error);
            return false;
        }
    }

    // 获取已保存的状态
    getSavedStates() {
        try {
            const data = localStorage.getItem(this.shareKey);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('获取保存状态失败:', error);
            return [];
        }
    }

    // 删除保存的状态
    deleteSavedState(index) {
        const savedStates = this.getSavedStates();
        if (index >= 0 && index < savedStates.length) {
            savedStates.splice(index, 1);
            try {
                localStorage.setItem(this.shareKey, JSON.stringify(savedStates));
                return true;
            } catch (error) {
                console.error('删除状态失败:', error);
            }
        }
        return false;
    }

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
                return true;
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                const result = document.execCommand('copy');
                textArea.remove();
                return result;
            }
        } catch (error) {
            console.error('复制到剪贴板失败:', error);
            return false;
        }
    }

    // 导出状态数据
    exportState(format = 'json') {
        const state = this.getCurrentState({ 
            includeLayers: true, 
            includeEntities: true,
            includeTerrain: true
        });
        
        let content = '';
        let filename = '';
        let mimeType = '';
        
        switch (format.toLowerCase()) {
            case 'json':
                content = JSON.stringify(state, null, 2);
                filename = `cesium_state_${Date.now()}.json`;
                mimeType = 'application/json';
                break;
            case 'url':
                const url = this.generateShareUrl({ 
                    includeLayers: true, 
                    includeEntities: true 
                });
                content = url;
                filename = `cesium_share_url_${Date.now()}.txt`;
                mimeType = 'text/plain';
                break;
            default:
                console.error('不支持的导出格式:', format);
                return false;
        }
        
        // 创建下载链接
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
        
        return true;
    }

    // 导入状态数据
    importState(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    let state = null;
                    const content = e.target.result;
                    
                    // 尝试解析JSON
                    if (content.trim().startsWith('{')) {
                        state = JSON.parse(content);
                    } else if (content.includes('share=')) {
                        // 从URL提取状态
                        const url = new URL(content);
                        const shareParam = url.searchParams.get('share');
                        if (shareParam) {
                            state = this.decodeState(shareParam);
                        }
                    }
                    
                    if (state && this.applyState(state)) {
                        resolve(state);
                    } else {
                        reject(new Error('无效的状态文件'));
                    }
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsText(file);
        });
    }

    // 获取分享统计信息
    getShareStats() {
        const savedStates = this.getSavedStates();
        return {
            totalShares: savedStates.length,
            oldestShare: savedStates.length > 0 ? 
                Math.min(...savedStates.map(s => new Date(s.createTime).getTime())) : null,
            newestShare: savedStates.length > 0 ? 
                Math.max(...savedStates.map(s => new Date(s.createTime).getTime())) : null
        };
    }
}

// 导出到全局
window.ShareTool = ShareTool;