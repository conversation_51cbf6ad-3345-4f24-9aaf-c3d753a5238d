/* 标题样式 */

.title-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
    pointer-events: none;
    height: 30px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.title-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    position: absolute;
    z-index: 1;
}

.title-text {
    position: relative;
    z-index: 2;
    color: white;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    letter-spacing: 10px;
    text-align: center;
    width: 100%;
    margin-left: -5px;
    font-family: "Microsoft YaHei", "微软雅黑", "SimHei", "黑体", sans-serif;
}