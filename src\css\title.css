/* 🎯 超炫酷标题样式 */

.title-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
    pointer-events: none;
    height: 80px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(180deg,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(0, 0, 0, 0.4) 50%,
        transparent 100%
    );
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(64, 224, 255, 0.2);
}

.title-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        transparent,
        rgba(64, 224, 255, 0.8),
        rgba(100, 149, 237, 0.6),
        transparent
    );
    animation: titleGlow 3s ease-in-out infinite;
}

@keyframes titleGlow {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

.title-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    position: absolute;
    z-index: 1;
    opacity: 0.3;
    filter: blur(1px);
}

.title-text {
    position: relative;
    z-index: 2;
    color: #40E0FF;
    font-size: 32px;
    font-weight: 900;
    text-shadow:
        0 0 20px rgba(64, 224, 255, 0.8),
        0 0 40px rgba(64, 224, 255, 0.4),
        0 4px 8px rgba(0, 0, 0, 0.5);
    letter-spacing: 12px;
    text-align: center;
    width: 100%;
    margin-left: -6px;
    font-family: 'Segoe UI', "Microsoft YaHei", "微软雅黑", "SimHei", "黑体", sans-serif;
    text-transform: uppercase;
    animation: titlePulse 4s ease-in-out infinite;
    background: linear-gradient(45deg,
        #40E0FF,
        #6495ED,
        #40E0FF
    );
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titlePulse 4s ease-in-out infinite, gradientShift 6s ease-in-out infinite;
}

@keyframes titlePulse {
    0%, 100% {
        transform: scale(1);
        filter: brightness(1);
    }
    50% {
        transform: scale(1.02);
        filter: brightness(1.2);
    }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 📱 响应式标题 */
@media (max-width: 768px) {
    .title-container {
        height: 60px;
    }

    .title-text {
        font-size: 24px;
        letter-spacing: 8px;
        margin-left: -4px;
    }
}

@media (max-width: 480px) {
    .title-container {
        height: 50px;
    }

    .title-text {
        font-size: 18px;
        letter-spacing: 6px;
        margin-left: -3px;
    }
}